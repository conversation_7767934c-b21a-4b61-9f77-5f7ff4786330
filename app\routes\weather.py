import os
import logging
from typing import Annotated
from contextlib import asynccontextmanager
from fastapi import APIRouter, HTTPException, Depends, status, Request, UploadFile, File

from schemas.users import User
from schemas.weather import WeatherDataResponse, WeatherDataRequest
from services.auth import get_current_active_user
from services.api_log import ApiLogsService
from services.weather import WeatherService



# setup logging
logger = logging.getLogger(__name__)  # Add logger
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"

router = APIRouter()
current_user = Depends(get_current_active_user)


@router.post(
    "/get-location-weather",
    response_model=WeatherDataResponse,
    summary="Get weather data for a location"
)
async def get_weather_data(
    data: WeatherDataRequest,
    current_user: Annotated[User, current_user],
    request: Request,
):
    """Get weather data for a location"""
    return WeatherService().get_weather_data(data.location)
