import json
from config import <PERSON><PERSON><PERSON><PERSON>
from langchain_core.messages import HumanMessage
from langchain_google_genai import ChatGoogleGenerativeAI


# Load env secrets
secrets = SecretsManager().get_secrets()



class GeminiClient:
    """ GeminiClient class for interacting with the Gemini API. """

    def __init__(self, model: str = None, temperature: float = 0.0):
        """
        Initializes the GeminiClient by loading environment variables
        and setting up the Gemini API client.
        """
        self.api_key = secrets['GEMINI_API_KEY']
        self.model = model if model else "gemini-2.0-flash"
        self.temperature = temperature

    def __str__(self):
        return f"GeminiClient(model={self.model}, temperature={self.temperature})"

    def prompt_chat_model(self, prompt: str, output_format: str = "json"):
        """
        Prompts the Gemini model with the provided prompt.

        :param prompt: The prompt to send to the chat model
        :param output_format: The output format for the response
        :return: The response from the chat model
        """
        
        # Configure and create Gemini client
        model = ChatGoogleGenerativeAI(
            google_api_key=self.api_key,
            model=self.model,
            temperature=self.temperature
        )

        # Get response from model
        response = model.invoke(prompt)
        response_content = response.content

        # Handle JSON formatting if requested
        if output_format != "json":
            return response_content

        # Clean JSON response if wrapped in markdown
        if "```json\n" in response_content:
            response_content = response_content.split("```json\n")[1]
        if "\n```" in response_content:
            response_content = response_content.split("\n```")[0]

        return json.loads(response_content)
    
    
    def prompt_with_image(self, prompt_text: str, image_paths: list, output_format: str = "json"):
            """
            Sends a multimodal prompt combining text and images (from local files, remote URLs, or S3 URIs)
            to the Gemini model.

            Args:
                prompt_text (str): The text prompt to guide the model's analysis.
                image_paths (list): List of image paths, which can be:
                    - Local file paths,
                    - Remote URLs (http/https), or
                    - S3 URIs (e.g. "s3://your-bucket/path/to/image.jpg").
                output_format (str, optional): Response format ("json" or "text"). Defaults to "json".

            Returns:
                Union[dict, str]: Parsed JSON dict if output_format="json", or raw string otherwise.

            Raises:
                ValueError: If JSON parsing fails.
                FileNotFoundError: If an image (local or S3) cannot be accessed.
                TypeError: If arguments are of incorrect type.
            """
            
            model = ChatGoogleGenerativeAI(
                google_api_key=self.api_key,
                model="gemini-1.5-flash-8b",  # TODO: fix issue with gemini-2.0-flash not accepting image prompts
                temperature=self.temperature
            )
            
            content = [{"type": "text", "text": prompt_text}]
            
            for img_path in image_paths:
                content.append({
                    "type": "image_url",
                    "image_url": img_path
                })
            
            message = HumanMessage(content=content)
            response = model.invoke([message])
            response_content = response.content.strip()

            if output_format != "json":
                return response_content

            # Remove markdown formatting for JSON if present.
            json_string = response_content
            if json_string.count("```json") == 1:
                json_string = json_string.split("```json")[1].split("```")[0]

            try:
                return json.loads(json_string)
            except json.JSONDecodeError:
                raise ValueError(
                    f"Failed to parse JSON response. Raw model output:\n{response_content}"
                )
