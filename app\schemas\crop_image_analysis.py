from enum import Enum
from pydantic import BaseModel
from typing import List, Optional


class CropHealthStatus(str, Enum):
    Healthy = "healthy"
    Stressed = "stressed"
    Diseased = "diseased"
    Uncertain = "uncertain"


class CropImageAnalysisRequest(BaseModel):
    image_path: str


class SimilarCrop(BaseModel):
    target_crop: str
    similarity_score: float
      

class CropImageAnalysis(BaseModel):
    name: str
    health_status: CropHealthStatus
    observations: Optional[str] = None
    similar_crops: List[SimilarCrop]

    class Config:
        from_attributes = True


class CropImageAnalysisList(BaseModel):
    crops: List[CropImageAnalysis]
    total: int

    class Config:
        from_attributes = True
