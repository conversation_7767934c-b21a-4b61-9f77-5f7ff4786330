import re
import requests
from typing import List, Optional, Union, Tuple, Dict
from datetime import datetime, timezone, timedelta

from config import secrets, logger
from schemas.weather import CurrentWeatherData, ForecastWeatherData, WeatherData


class GoogleWeatherMapService:
    def __init__(self):
        self.root_api_url = "https://weather.googleapis.com/v1"
        self.geocoding_api_url = "https://maps.googleapis.com/maps/api/geocode/json"
        self.api_key = secrets.get("GOOGLE_MAPS_API_KEY")
        
        if not self.api_key:
            logger.error("Google Maps API key is not configured")
            raise ValueError("Google Maps API key is not configured")

    def _validate_coordinates(self, lat: float, lon: float) -> None:
        """Validate the given coordinates."""
        if not (-90 <= lat <= 90) or not (-180 <= lon <= 180):
            logger.error(f"Invalid coordinates: lat={lat}, lon={lon}")
            raise ValueError("Invalid coordinates. Latitude must be between -90 and 90, longitude between -180 and 180")

    def geocode_address(self, location_name: str) -> Optional[Tuple[float, float, str, str]]:
        """
        Convert an address to coordinates using Google Maps Geocoding API.
        
        Args:
            location_name (str): Address to geocode
            
        Returns:
            Optional[Tuple[float, float, str, str]]: Tuple of (latitude, longitude, location, country_code) if successful, None if failed
            
        Raises:
            requests.RequestException: If there's an error with the API request
        """
        try:
            # Make the geocoding request
            url = f"{self.geocoding_api_url}?address={location_name}&key={self.api_key}"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if data['status'] != 'OK':
                logger.error(f"Geocoding API error: {data['status']}")
                return None

            # Get the first result's location
            location = data['results'][0]['geometry']['location']
            lat, lon = location['lat'], location['lng']

            # Extract administrative level 2 (county/district) and country code
            adm_level2 = None
            country_code = None
            
            for component in data['results'][0]['address_components']:
                if 'administrative_area_level_2' in component['types']:
                    adm_level2 = component['long_name']
                elif 'country' in component['types']:
                    country_code = component['short_name']
            
            # If we couldn't find adm_level2, try to use locality (city) as fallback
            if not adm_level2:
                for component in data['results'][0]['address_components']:
                    if 'locality' in component['types']:
                        adm_level2 = component['long_name']
                        break
            
            # Validate the coordinates
            self._validate_coordinates(lat, lon)
            
            # logger.info(f"Successfully geocoded address '{address}' to coordinates lat={lat}, lon={lon}, location={adm_level2}, country={country_code}")
            return lat, lon, adm_level2, country_code
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error geocoding address '{location_name}': {str(e)}")
            if hasattr(e, 'response') and e.response:
                logger.error(f"API Response: {e.response.text}")
        except (KeyError, ValueError) as e:
            logger.error(f"Error parsing geocoding response for '{location_name}': {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error geocoding address '{location_name}': {str(e)}")
        
        return None

    def _parse_timestamp(self, timestamp_str: str) -> datetime:
        """Parse timestamp with microsecond truncation."""
        current_time = timestamp_str.replace('Z', '+00:00')
        
        # Handle microseconds with more than 6 digits by truncating to 6 digits
        pattern = r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})\.(\d+)(\+\d{2}:\d{2})'
        match = re.match(pattern, current_time)
        
        if match:
            date_part, microseconds, timezone_part = match.groups()
            microseconds = microseconds[:6].ljust(6, '0')
            current_time = f"{date_part}.{microseconds}{timezone_part}"
        
        forecast_date = datetime.fromisoformat(current_time)
        forecast_date = forecast_date.replace(microsecond=0)
        return forecast_date.astimezone(timezone.utc)

    def _parse_current_weather(self, data: dict) -> CurrentWeatherData:
        """Parse current weather data from Google Weather API."""
        weather_date = self._parse_timestamp(data['currentTime'])
        
        return CurrentWeatherData(
            temp=data['temperature']['degrees'],
            feels_like=data['feelsLikeTemperature']['degrees'],
            temp_min=data['currentConditionsHistory']['minTemperature']['degrees'],
            temp_max=data['currentConditionsHistory']['maxTemperature']['degrees'],
            pressure=data['airPressure']['meanSeaLevelMillibars'],
            humidity=data['relativeHumidity'],
            wind_speed=data['wind']['speed']['value'],
            wind_deg=data['wind']['direction']['degrees'],
            cloud_cover=data.get('cloudCover', 0),
            country_code=None,  # Not provided in Google Weather API
            condition=data['weatherCondition']['description']['text'],
            location_name=None,  # Not provided in Google Weather API
            lat=data['location']['latitude'],
            lon=data['location']['longitude']
        )

    def _parse_forecast(self, data: dict) -> List[ForecastWeatherData]:
        """Parse forecast data from Google Weather API."""
        forecasts = []
        now = datetime.now(timezone.utc)
        cutoff_date = now + timedelta(days=5)

        for day in data.get('forecastDays', []):
            # Process daytime forecast
            daytime = day.get('daytimeForecast', {})
            if daytime:
                forecast_date = self._parse_timestamp(daytime['interval']['startTime'])
                
                if forecast_date > cutoff_date:
                    continue

                forecasts.append(ForecastWeatherData(
                    weather_date=forecast_date,
                    weather_description=daytime['weatherCondition']['description']['text'],
                    temp=day['maxTemperature']['degrees'],
                    temp_min=day['minTemperature']['degrees'],
                    temp_max=day['maxTemperature']['degrees'],
                    feels_like=day['feelsLikeMaxTemperature']['degrees'],
                    humidity=daytime['relativeHumidity']
                ))

            # Process nighttime forecast
            nighttime = day.get('nighttimeForecast', {})
            if nighttime:
                forecast_date = self._parse_timestamp(nighttime['interval']['startTime'])
                
                if forecast_date > cutoff_date:
                    continue

                forecasts.append(ForecastWeatherData(
                    weather_date=forecast_date,
                    weather_description=nighttime['weatherCondition']['description']['text'],
                    temp=day['minTemperature']['degrees'],
                    temp_min=day['minTemperature']['degrees'],
                    temp_max=day['maxTemperature']['degrees'],
                    feels_like=day['feelsLikeMinTemperature']['degrees'],
                    humidity=nighttime['relativeHumidity']
                ))

        return forecasts

    def get_current_weather_by_coordinates(self, lat: float, lon: float) -> Optional[CurrentWeatherData]:
        """
        Get current weather data for a location by coordinates.
        
        Args:
            lat (float): Latitude
            lon (float): Longitude
            
        Returns:
            Optional[CurrentWeatherData]: Weather data if successful, None if failed
            
        Raises:
            ValueError: If coordinates are invalid
            requests.RequestException: If there's an error with the API request
        """
        self._validate_coordinates(lat, lon)

        try:
            url = f"{self.root_api_url}/currentConditions:lookup?key={self.api_key}&location.latitude={lat}&location.longitude={lon}"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            # Add location data to the response
            data['location'] = {'latitude': lat, 'longitude': lon}
            weather_data = self._parse_current_weather(data)
            
            # logger.info(f"Successfully retrieved weather data for coordinates lat={lat}, lon={lon}")
            return weather_data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching weather data for coordinates lat={lat}, lon={lon}: {str(e)}")
            if hasattr(e, 'response') and e.response:
                logger.error(f"API Response: {e.response.text}")
        except (KeyError, ValueError) as e:
            logger.error(f"Error parsing weather data for coordinates lat={lat}, lon={lon}: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error getting weather data for coordinates lat={lat}, lon={lon}: {str(e)}")
        
        return None

    def get_weather_forecast_by_coordinates(self, lat: float, lon: float) -> List[ForecastWeatherData]:
        """
        Get weather forecast data for a location by coordinates.
        
        Args:
            lat (float): Latitude
            lon (float): Longitude
            
        Returns:
            List[ForecastWeatherData]: List of forecast data if successful, empty list if failed
            
        Raises:
            ValueError: If coordinates are invalid
            requests.RequestException: If there's an error with the API request
        """
        self._validate_coordinates(lat, lon)

        try:
            url = f"{self.root_api_url}/forecast/days:lookup?key={self.api_key}&location.latitude={lat}&location.longitude={lon}&days=5"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            forecasts = self._parse_forecast(data)
            
            # logger.info(f"Successfully retrieved forecast data for coordinates lat={lat}, lon={lon}")
            return forecasts
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching forecast data for coordinates lat={lat}, lon={lon}: {str(e)}")
            if hasattr(e, 'response') and e.response:
                logger.error(f"API Response: {e.response.text}")
        except (KeyError, ValueError) as e:
            logger.error(f"Error parsing forecast data for coordinates lat={lat}, lon={lon}: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error getting forecast data for coordinates lat={lat}, lon={lon}: {str(e)}")
        
        return []

    def get_weather_data_by_location_name(self, location_name: str) -> WeatherData:
        """
        Get current weather data for a location.
        
        Args:
            location_name (str): City name or location query
            
        Returns:
            Optional[CurrentWeatherData]: Weather data if successful, None if failed
            
        Raises:
            ValueError: If coordinates are invalid or if country is missing for string location
            requests.RequestException: If there's an error with the API request
        """
            
        coords = self.geocode_address(location_name)
        if not coords:
            return None
        
        lat, lon, adm_level2, country_code = coords
        current_weather = self.get_current_weather_by_coordinates(lat, lon)
        forecast_weather = self.get_weather_forecast_by_coordinates(lat, lon)
        
        return WeatherData(
            location_name=location_name,
            current_weather=current_weather,
            forecast_weather=forecast_weather
        )
