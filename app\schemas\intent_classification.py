from pydantic import BaseModel
from typing import Optional

class IntentClassificationInput(BaseModel):
    """
    Schema for the input query for the intent classification endpoint.
    """
    query: str

class IntentClassificationOutput(BaseModel):
    """
    Schema for the output response of the intent classification endpoint.
    """
    query: str
    intent: str
    location: Optional[str] = None

    