import langchain
from fastapi import HTTPException, status
from langchain_core.prompts import ChatPromptTemplate

from config import secrets, logger
from utils.llm_openai import OpenAIClient

import schemas.fertilizer_recommendations as schema

# config
langchain.verbose = False
langchain.debug = False
langchain.llm_cache = False


class FertilizerRecommendationMLService:
    pass


class FertilizerRecommendationLLMService:
    llm = OpenAIClient(
        model=secrets['OPENAI_DEFAULT_MODEL'],
        temperature=0.0
    )

    def fertilizer_recommendation_llm_prompt(self, data: schema.FertilizerRecommendation):
        """Prompt the LLM with fertilizer recommendation data"""
        prompt_text = """
        You are an expert agricultural advisor. You will be provided with soil test data, crop information, and a fertilizer product catalog. Your task is to analyze the data and recommend the most suitable fertilizer for the specified crop, explaining your reasoning.

        Soil Texture: {soil_texture}
        soil pH: {soil_ph}
        Nitrogen (%): {soil_nitrogen_ppm}
        Phosphorus (%): {soil_phosphorus_ppm}
        Potassium (%): {soil_potassium_ppm}
        Soil Moisture (%): {soil_moisture_pct}
        Organic Matter (%): {soil_organic_matter_pct}
        Temperature (°C): {soil_temperature}
        Crops: {crops}

        Here is a fertilizer product catalog (Product Name|Type|N%|P2O5%|K2O%|Other Nutrients):
        Urea|Synthetic|46|0|0|
        Neem Coated Urea|Synthetic|46|0|0|Neem Coating
        Calcium Nitrate|Synthetic|15|0|0|19% Calcium
        NPK 15-15-15|Blended|15|15|15|Trace elements (often)
        NPK 20-10-10|Blended|20|10|10|Trace elements (often)
        NPK 20-20-20|Blended|20|20|20|Trace elements (often)
        NPK 12-12-17|Blended|12|12|17|
        NPK 27-13-13|Blended|27|13|13|
        NPK 13-0-44|Blended|13|0|44|
        NPK 12-12-17 + MgO|Blended|12|12|17|Magnesium Oxide (MgO)
        NPK 15-15-15 + 10.2S|Blended|15|15|15|10.2% Sulfur
        NPK 20-10-10 + 10.2S|Blended|20|10|10|10.2% Sulfur
        NPK 20-10-10 + 15.2S|Blended|20|10|10|15.2% Sulfur
        Single Super Phosphate (SSP)|Synthetic|0|20|0|10-12% Sulfur
        Tripple Super Phosphate (TSP)|Synthetic|0|46|0|
        Magnesium Phosphate (Kieserite)|Natural/Mined|0|0|0|Magnesium, Sulfur
        Suphate of Potash|Natural/Mined|0|0|50|Sulphur
        Potassium Nitrate|Synthetic|13|0|44|   
        
        1. Based on these parameters, provide a concise agronomic interpretation.
        2. Recommend a suitable fertilizer, including the quantity, application time, and method.
        
        Take into account the soil type, pH, nutrient levels, organic matter, temperature, rainfall, humidity, crop type, and field size.
        
        First, analyze the soil test data and identify any nutrient deficiencies for the given crop. Then, based on the fertilizer catalog, recommend the most suitable fertilizer to address these deficiencies, considering the soil pH and the crop's requirements.        
        
        Return the response in JSON format using the below structure:
        {{
            "recommendations": [
                {{
                    "fertilizer": "NPK 15-15-15",
                    "quantity": 100,
                    "application_time": "Pre-planting",
                    "application_method": "Broadcasting",
                    "explanation": "This balanced fertilizer is suitable for most crops and provides a good mix of N, P, and K."
                }}
            ]
        }}
        """
        messages = [
            ("system", "Determining fertilizer recommendations..."),
            ("human", prompt_text)
        ]
        prompt = ChatPromptTemplate(messages).invoke(
            {
                "soil_texture": data.soil_texture,
                "soil_ph": data.soil_ph,
                "soil_nitrogen_ppm": data.soil_nitrogen_ppm,
                "soil_phosphorus_ppm": data.soil_phosphorus_ppm,
                "soil_potassium_ppm": data.soil_potassium_ppm,
                "soil_moisture_pct": data.soil_moisture_pct,
                "soil_organic_matter_pct": data.soil_organic_matter_pct,
                "soil_temperature": data.soil_temperature,
                "crops": data.crops
            }
        )
        return self.llm.prompt_chat_model(prompt)

    def get_fertilizer_recommendations(self, data: schema.FertilizerRecommendation):
        """Get fertilizer recommendation"""
        try:
            response = self.fertilizer_recommendation_llm_prompt(data)

            # Parse response
            recommendations = response['recommendations']
            return schema.FertilizerRecommendationResponseList(
                recommendations=recommendations
            )
        except Exception as e:
            logger.error(f"Error getting fertilizer recommendation: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error getting fertilizer recommendation"
            )
