####################################################
# Secrets Variables
####################################################

variable "ENV" {
  description = "The environment"
  type        = string
  default     = "staging"
}

variable "DB_SCHEMA" {
  description = "The name of the database schema"
  type        = string
}

variable "DB_URI" {
  description = "The URI of the database"
  type        = string
}

variable "AWS_SECRET_ACCESS_KEY" {
  description = "The AWS secret access key"
  type        = string
}

variable "AWS_ACCESS_KEY_ID" {
  description = "The AWS access key ID"
  type        = string
}

variable "AWS_S3_BUCKET" {
  description = "The AWS S3 bucket"
  type        = string
}

variable "AWS_REGION" {
  description = "The AWS region"
  type        = string
  default     = "eu-central-1"
}

variable "JWT_SECRET_KEY" {
  description = "The secret key for JWT"
  type        = string
}

variable "JWT_REFRESH_SECRET_KEY" {
  description = "The secret key for JWT refresh"
  type        = string
}

variable "OPENAI_API_KEY" {
  description = "The OpenAI API key"
  type        = string
}

variable "OPENAI_DEFAULT_MODEL" {
  description = "The default model for OpenAI"
  type        = string
}

variable "GEMINI_API_KEY" {
  description = "The Gemini API key"
  type        = string
}

variable "GEMINI_DEFAULT_MODEL" {
  description = "The default model for Gemini"
  type        = string
}

variable "DEEPSEEK_API_KEY" {
  description = "The DeepSeek API key"
  type        = string
}

variable "DEEPSEEK_DEFAULT_MODEL" {
  description = "The default model for DeepSeek"
  type        = string
}

variable "GROK_API_KEY" {
  description = "The Grok API key"
  type        = string
}

variable "LLAMA_DEFAULT_MODEL" {
  description = "The default model for Llama"
  type        = string
}

variable "SENTRY_DSN" {
  description = "The Sentry DSN"
  type        = string
}

variable "MONGODB_URI" {
  description = "The MongoDB URI"
  type        = string
}

variable "GOOGLE_MAPS_API_KEY" {
  description = "The Google Maps API key"
  type        = string
}

variable "OPENWEATHERMAP_API_KEY" {
  description = "The OpenWeatherMap API key"
  type        = string
}

variable "GOOGLE_TEXT_TO_SPEECH_API_KEY" {
  description = "The Google Cloud Text to Speech API Key"
  type        = string
}

####################################################

variable "runtime" {
  description = "Python runtime version"
  type        = string
  default     = "python3.11"
}
