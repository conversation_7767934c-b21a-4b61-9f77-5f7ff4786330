from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
import uuid


class SoilTestResult(BaseModel):
    test_date: Optional[datetime] = None
    test_type: Optional[str] = None
    soil_texture: Optional[str] = None
    soil_moisture_pct: Optional[float] = None
    soil_ec: Optional[float] = None  
    soil_ph: Optional[float] = None
    soil_nitrogen_ppm: Optional[float] = None
    soil_phosphorus_ppm: Optional[float] = None
    soil_potassium_ppm: Optional[float] = None
    soil_calcium_ppm: Optional[float] = None
    soil_magnesium_ppm: Optional[float] = None
    soil_sulfur_ppm: Optional[float] = None
    soil_organic_matter_pct: Optional[float] = None
    soil_micronutrients: Optional[Dict[str, Any]] = Field(default_factory=dict)

    class Config:
        from_attributes = True


class SoilTestResponse(BaseModel):
    status: str
    message: str
    data: List[SoilTestResult] = Field(default_factory=list)  
    error_code: Optional[str] = None
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat() + "Z")
    request_id: str = Field(default_factory=lambda: str(uuid.uuid4()))

    class Config:
        from_attributes = True
