from datetime import datetime
from pydantic import BaseModel
from typing import List, Optional


class CurrentWeatherData(BaseModel):
    temp: float
    feels_like: float
    temp_min: float
    temp_max: float    
    pressure: float
    humidity: float
    wind_speed: float
    wind_deg: float
    cloud_cover: float
    country_code: Optional[str]
    condition: Optional[str]
    location_name: Optional[str]
    lat: Optional[float]
    lon: Optional[float]


class ForecastWeatherData(BaseModel):
    """Forecast weather data model"""
    weather_date: datetime
    weather_description: str
    temp: float
    temp_min: float
    temp_max: float
    feels_like: float
    humidity: float

    def model_dump(self) -> dict:
        """Convert model to dict with formatted date"""
        data = super().model_dump()
        # Format the date to ISO format
        data['weather_date'] = data['weather_date'].isoformat()
        return data
    

class WeatherData(BaseModel):
    location_name: str
    current_weather: CurrentWeatherData
    forecast_weather: List[ForecastWeatherData]

    class Config:
        from_attributes = True


class WeatherDataRequest(BaseModel):
    location: str

    class Config:
        from_attributes = True    

class WeatherDataResponse(BaseModel):
    status: str
    message: str
    data: WeatherData
    error_code: Optional[str] = None
    timestamp: str = datetime.utcnow().isoformat() + "Z"

    class Config:
        from_attributes = True
