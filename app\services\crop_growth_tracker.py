import langchain
from fastapi import HTT<PERSON>Ex<PERSON>, status
from langchain_core.prompts import ChatPromptTemplate

from config import secrets, logger
import schemas.crop_growth_tracker as schema
from utils.llm_openai import OpenAIClient
from utils.llm_deepseek import DeepseekClient
from utils.llm_gemini import GeminiClient

# config
langchain.verbose = False
langchain.debug = False
langchain.llm_cache = False


class CropGrowthTrackerMLService:
    pass


class CropGrowthTrackerLLMService:
    llm = DeepseekClient(
        model=secrets['DEEPSEEK_DEFAULT_MODEL'], 
        temperature=0.0
    )
    '''
    llm = GeminiClient(
        model=secrets['GEMINI_DEFAULT_MODEL'], 
        temperature=0.0
    )
    '''

    def crop_growth_tracker_llm_prompt(self, data: schema.FieldCropData) -> dict:
        """Prompt the LLM with crop growth tracker data"""
        prompt_text = """
        You are an expert agricultural scientist specializing in crop growth forecasting. Based on the following crop data, provide a detailed growth stage forecast:

        Crop Information:
        - Crop Name: {crop_name}
        - Crop Variant: {crop_variant}
        - Country: {country_grown}
        - Planting Date: {planting_date}

        Provide a comprehensive growth stage forecast for all stages: Germination, Seedling, Vegetative, Reproductive, Maturity, and Harvesting.

        IMPORTANT: Your response MUST be a valid JSON object with the following structure:
        {{
            "growth_stages": [
                {{
                    "growth_stage": "Germination",
                    "start_date": "YYYY-MM-DD",
                    "end_date": "YYYY-MM-DD",
                    "timeline_duration_min": number,
                    "timeline_duration_max": number,
                    "growth_stage_treatments": ["treatment1", "treatment2", ...]
                }},
                // ... repeat for each growth stage
            ],
            "timeline": "Days",          
            "total_duration": number,
            "expected_harvest_date": "YYYY-MM-DD"
        }}

        For each growth stage, include:
        1. Accurate start and end dates based on the planting date
        2. Duration in the selected timeline unit (Days)
        3. Specific treatments and care practices
        4. Environmental considerations for the given country

        Base your predictions on:
        1. Standard growth patterns for {crop_name} ({crop_variant})
        2. Growing conditions in {country_grown}
        3. Seasonal variations and climate factors
        4. Best practices for optimal growth

        Ensure all dates are in ISO format (YYYY-MM-DD) and all durations are realistic numbers.
        """

        messages = [
            ("system", "You are an expert agricultural scientist specializing in crop growth forecasting. Provide accurate and practical growth stage predictions based on the given crop data. Always respond with a valid JSON object containing a growth_stages array and appropriate timeline unit."),
            ("human", prompt_text)
        ]

        prompt = ChatPromptTemplate(messages).invoke(
            {
                "crop_name": data.crop_name,
                "crop_variant": data.crop_variant or None,
                "country_grown": data.country_grown or None,
                "planting_date": data.planting_date
            }
        )
        return self.llm.prompt_chat_model(prompt)

    def get_crop_growth_tracker(self, data: schema.FieldCropData) -> schema.CropGrowthTracker:
        """Get crop growth tracker"""
        try:
            response = self.crop_growth_tracker_llm_prompt(data)

            # Parse response
            return schema.CropGrowthTracker(
                crop_name=data.crop_name,
                crop_variant=data.crop_variant,
                country_grown=data.country_grown,
                planting_date=data.planting_date,
                timeline=response['timeline'],
                total_duration=response['total_duration'],
                expected_harvest_date=response['expected_harvest_date'],
                growth_stages=response['growth_stages']
            )
        except Exception as e:
            logger.error(f"Error getting crop growth tracker: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error getting crop growth tracker"
            )
