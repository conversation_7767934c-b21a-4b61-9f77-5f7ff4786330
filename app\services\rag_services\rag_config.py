import os
from dataclasses import dataclass, field
from typing import Optional
from dotenv import load_dotenv
from config import secrets, logger

# Load .env file at the start when this module is imported
load_dotenv()

@dataclass
class S3Config:
    """Configuration specific to S3 access"""
    endpoint_url: Optional[str] = None
    aws_access_key_id: Optional[str] = None
    aws_secret_access_key: Optional[str] = None
    region_name: Optional[str] = None
    aws_bucket: Optional[str] = None
    rag_documents_prefix: Optional[str] = ""
    vectorstore_prefix: Optional[str] = ""


@dataclass
class Settings:
    """Loads and holds all application configuration settings from environment variables"""
    ENV: str = os.getenv('DB_SCHEMA', 'dev')
    DEVICE: str = 'cpu'
    USE_S3: bool =  'True'
    PROCESS_PDFS_ON_STARTUP: bool = os.getenv("PROCESS_PDFS_ON_STARTUP", "False").lower() in ('true', '1', 't', 'y', 'yes')

    # S3 Config (Nested)
    S3_CONFIG: S3Config = field(default_factory=lambda: S3Config(
        aws_access_key_id=secrets["AWS_ACCESS_KEY_ID"],
        aws_secret_access_key=secrets["AWS_SECRET_ACCESS_KEY"],
        endpoint_url=os.getenv('AWS_ENDPOINT_URL'),
        region_name='eu-central-1',
        aws_bucket=secrets["AWS_S3_BUCKET"],
        rag_documents_prefix=f'{Settings.ENV}/documents/farm-advisory-rag', # root folder of RAG documents (txt/pdf)
        vectorstore_prefix = f"{Settings.ENV}/vectorstores/25-06-03" # this is the location of FAISS files
    ))

    # Embeddings & Vector Stores
    VECTOR_STORE_BACKEND: str = os.getenv("VECTOR_STORE_BACKEND", "s3").lower()
    EMBEDDING_MODEL: str = os.getenv('EMBEDDING_MODEL', 'sentence-transformers/all-MiniLM-L6-v2')
    TOP_K: int = 2

    # Vector Store Settings
    CHROMA_BATCH_SIZE: int = 1000
    CHROMA_PERSIST_INTERVAL: int = 5 # minutes

    # Default LLM Configuration
    LLM_MODELS_MAPPER: dict = field(default_factory=lambda: {
        "gemini": secrets["GEMINI_DEFAULT_MODEL"],
        "llama": secrets["LLAMA_DEFAULT_MODEL"],
        "openai": secrets["OPENAI_DEFAULT_MODEL"],
        "deepseek": secrets["DEEPSEEK_DEFAULT_MODEL"]
    })
    LLM_PROVIDER: str =  'gemini'
    LLM_MODEL: Optional[str] = field(default=None)
    LLM_TEMPERATURE: float =  0.0
    CONTEXT_WINDOW_SIZE: int = os.getenv('CONTEXT_WINDOW_SIZE', 5)

    # API Keys (Directly read from environment)
    GEMINI_API_KEY: Optional[str] = secrets["GEMINI_API_KEY"]
    DEEPSEEK_API_KEY: Optional[str] = secrets["DEEPSEEK_API_KEY"]
    OPENAI_API_KEY: Optional[str] = secrets["OPENAI_API_KEY"]
    GROQ_API_KEY: Optional[str] = secrets["GROK_API_KEY"]
    #HUGGINGFACEHUB_API_TOKEN: Optional[str] = os.getenv("HUGGINGFACEHUB_API_TOKEN") # If needed by embeddings/models

    def __post_init__(self):
        """Post-initialization validation and defaults."""
        # Set environment variables required by some LangChain integrations
        if self.GEMINI_API_KEY: os.environ["GOOGLE_API_KEY"] = self.GEMINI_API_KEY
        if self.DEEPSEEK_API_KEY: os.environ["DEEPSEEK_API_KEY"] = self.DEEPSEEK_API_KEY
        if self.OPENAI_API_KEY: os.environ["OPENAI_API_KEY"] = self.OPENAI_API_KEY
        if self.GROQ_API_KEY: os.environ["GROQ_API_KEY"] = self.GROQ_API_KEY
        # if self.HUGGINGFACEHUB_API_TOKEN: os.environ["HUGGINGFACEHUB_API_TOKEN"] = self.HUGGINGFACEHUB_API_TOKEN

        # Initialize LLM_MODEL if not set
        if self.LLM_MODEL is None:
            self.LLM_MODEL = self.LLM_MODELS_MAPPER.get(self.LLM_PROVIDER, secrets["GEMINI_DEFAULT_MODEL"])

        # Simple validation for S3
        if self.USE_S3:
            s3_conf = self.S3_CONFIG
            creds_ok = all([s3_conf.aws_access_key_id, s3_conf.aws_secret_access_key, s3_conf.region_name])
            buckets_ok = s3_conf.aws_bucket and (s3_conf.aws_bucket if self.PROCESS_PDFS_ON_STARTUP else True)
            if not creds_ok: logger.warning("S3 enabled, but AWS credentials/region might be missing in config.")
            if not buckets_ok: logger.warning("S3 enabled, but required bucket names (TEXT_BUCKET, PDF_BUCKET) might be missing.")

        logger.info(f"Settings loaded: LLM Provider={self.LLM_PROVIDER}, Model={self.LLM_MODEL}, Use S3={self.USE_S3}, Device={self.DEVICE}")


# Create a single, importable instance of the settings
settings = Settings()
