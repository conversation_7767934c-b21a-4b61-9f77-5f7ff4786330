import json
from config import <PERSON><PERSON><PERSON><PERSON>
from langchain_core.messages import HumanMessage
from langchain_deepseek import ChatDeepSeek

# Load env secrets
secrets = SecretsManager().get_secrets()

class DeepseekClient:
    """Deepseek class for interacting with the Deepseek API."""

    def __init__(self, model: str = None, temperature: float = 0.0, timeout: float = 30.0):
        """
        Initializes the DeepseekClient by loading environment variables and setting up the Deepseek API client.
        """
        self.api_key = secrets["DEEPSEEK_API_KEY"]
        self.model = model if model else "deepseek-chat"
        self.temperature = temperature
        self.timeout: float = timeout

    def __str__(self):
        return f"DeepseekClient(model={self.model}, temperature={self.temperature})"

    def prompt_chat_model(self, prompt: str, output_format: str = "json"):
        """
        Prompts the Deepseek model with the provided prompt.
        """
        model = ChatDeepSeek(api_key=self.api_key, model=self.model, temperature=self.temperature)
        response = model.invoke(prompt)
        response = response.content

        if output_format != "json":
            return response

        if "```json\n" in response:
            response = response.split("```json\n")[1]

        if "\n```" in response:
            response = response.split("\n```")[0]

        return json.loads(response)

    def prompt_with_files(self, prompt_text: str, file_data: list[dict], output_format: str = "json"):
        """
        Sends a multimodal prompt with embedded base64 files to DeepSeek.
        """
        model = ChatDeepSeek(api_key=self.api_key, model=self.model, temperature=self.temperature)
        content = [
            {"type": "text", "text": prompt_text},
            {
                "type": "image",
                "image": f"data:{file_data[0]['mime_type']};base64,{file_data[0]['data']}",
            },
        ]
        message = HumanMessage(content=content)
        response = model.invoke([message])
        response = response.content.strip()

        if output_format != "json":
            return response

        json_string = response
        if json_string.count("```json") == 1:
            json_string = json_string.split("```json")[1].split("```")[0]

        try:
            return json.loads(response)
        except json.JSONDecodeError:
            raise ValueError(f"Failed to parse JSON response. Raw model output:\n{response}")
