from enum import Enum
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel


class SoilTypes(str, Enum):
    SANDY = "sandy"
    SILTY = "silty"
    CLAY = "clay"
    LOAM = "loam"
    PEAT = "peat"
    CHALKY = "chalky"
    SALT = "salt"


class CropTypes(str, Enum):
    pass


class SoilTestData(BaseModel):
    soil_texture: Optional[str] = None
    soil_ph: float
    soil_nitrogen_ppm: float
    soil_phosphorus_ppm: float
    soil_potassium_ppm: float
    soil_moisture_pct: Optional[float] = None
    soil_organic_matter_pct: Optional[float] = None
    soil_temperature: Optional[float] = None
    country: Optional[str]
    specified_preferences: Optional[List[str]] = None

    class Config:
        from_attributes = True


class CropRecommendation(BaseModel):
    crop_id: str
    crop_name: str
    suitability_score: float
    logic_suitability_score: Optional[float] = None
    llm_suitability_score: Optional[float] = None
    explanation: Optional[str] = None

    class Config:
        from_attributes = True


class CropRecommendationList(BaseModel):
    recommendations: List[CropRecommendation]
    total: int

    class Config:
        from_attributes = True
