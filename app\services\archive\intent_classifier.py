import spacy
import asyncio
from typing import Tuple, Dict, List, Optional

class QueryClassifier:
    """A class to classify user queries into predefined intents using SpaCy."""
    
    def __init__(self, model_name: str = "en_core_web_lg", similarity_threshold: float = 0.75):
        self.similarity_threshold = similarity_threshold
        self.nlp = self._load_spacy_model(model_name)
        self.examples = self._load_example_queries()
        self.examples_docs = self._process_examples()

    def _load_spacy_model(self, model_name: str) -> spacy.language.Language:
        try:
            return spacy.load(model_name)
        except OSError:
            error_message = (
                f"SpaCy '{model_name}' model not found. "
                f"Please run the following command in your terminal:\n"
                f"python -m spacy download {model_name}"
            )
            print("\n" + "="*80)
            print(error_message)
            print("="*80 + "\n")
            raise RuntimeError(error_message)
        
    def _load_example_queries(self) -> Dict[str, List[str]]:
        return {
            "Weather": [
                "What's the current weather like at my farm location?",
                "Will it rain tomorrow in my area?",
                "Give me the 7-day forecast for my fields.",
                "What's the temperature and humidity right now?",
                "Is there any storm expected this week?",
                "When is the next sunny period forecasted?",
                "What are the historical rainfall patterns for June?",
                "How will the wind speed affect my crops today?",
                "Show me the weather conditions for the upcoming planting season.",
                "Will there be frost tonight?"
            ],

            "Diagnoses" : [
                # Nutrient issues
                "What nutrient could my maize be missing if its growth is stunted and the lower leaves are purplish?",
                "Is my soil causing my pepper plants to wilt even after proper watering?",
                "My plants look generally pale and weak; could this be a nitrogen deficiency?",
                "My plants look like they have a nutrient deficiency.",
                "Why are my crops showing signs of malnutrition?",

                # Pest-related phrased as diagnosis
                "I found small bugs crawling on my maize, are they pests?",
                "My plants are infested with tiny insects, what are they?",
                "Are these aphids on my pepper plants causing damage?",
                "What kind of grubs are eating my potato roots?",
                "My plants are covered in sticky residue, could it be pests?",

                # Physical and visual symptoms
                "My peppers are wilting, what should I do?",
                "Why is my corn plant stunted and not growing?",
                "I found holes in the leaves of my cabbage.",
                "There are strange lesions on my groundnut plants.",
                "Are these deformed fruits normal on my citrus tree?",
                "My wheat leaves are turning yellow.",
                "The tomato crop has brown patches on its leaves.",
                "What causes the leaves on my cassava to turn purple?",
                "I see red discoloration on my corn leaves.",
                
                # General diagnostic questions
                "My crops look unhealthy, what's wrong?",
                "What's happening to my vegetables?",
                "My plants aren't growing well.",
                "How do I know if my crop has a disease?",
                "I need help diagnosing a problem with my farm.",
                "Could the weather be affecting my crops?",
                "Is this normal for my plants to look like this?",
                "Something's wrong with my plants."
            ],

            "Farm_Actions": [
                "Log my planting activity for maize on plot 5 today.",
                "Record the fertilizer application for my wheat crop.",
                "Update the harvest date for my tomato plants to next Tuesday.",
                "Add a new observation of pests in my field.",
                "Generate a yield report for the second quarter.",
                "Send me daily weather alerts for my farm.",
                "Initiate a new soil test analysis for plot 7.",
                "Register my new farm location and details.",
                "Delete the old crop record for my cassava from last year.",
                "Submit my irrigation schedule for approval.",
                "Upload my latest soil pH readings for analysis.",
                "Track a new disease outbreak for my potato crop."
            ],

            "General": [
                "How can I improve soil fertility for my crops?",
                "What's the best way to manage weeds organically?",
                "Describe sustainable farming practices for small farms.",
                "How do I choose the right fertilizer for my corn?",
                "What are common crop rotation strategies?",
                "Explain the fundamentals of hydroponic systems in agriculture.",
                "How can I increase my farm's yield per acre?",
                "What's the ideal planting depth for soybeans?",
                "Provide guidance on natural pest control methods for garden vegetables.",
                "What agricultural resources are available for new farmers?"
            ]
        }

    def _process_examples(self) -> Dict[str, List[spacy.tokens.Doc]]:
        return {intent: [self.nlp(query) for query in queries] for intent, queries in self.examples.items()}
    
    def _extract_location(self, text: str) -> Optional[str]:
        doc = self.nlp(text)
        for ent in doc.ents:
            if ent.label_ == "GPE":
                return ent.text
        return None

    async def classify_query(self, query: str) -> Tuple[str, float, Optional[str]]:
        query_doc = await asyncio.to_thread(self.nlp, query)
        max_similarity = 0.0
        best_intent = "General"  # Default fallback

        # Ensure query_doc has vectors before attempting similarity calculation
        if not query_doc.has_vector:
            return best_intent, 0.0, None
        
        for intent, docs in self.examples_docs.items():
            for example_doc in docs:
                if example_doc.has_vector:
                    similarity = query_doc.similarity(example_doc)
                    if similarity > max_similarity:
                        max_similarity = similarity
                        best_intent = intent

        if max_similarity < self.similarity_threshold:
            return "Ambiguous", round(max_similarity, 2), None 

        detected_location: Optional[str] = None
        if best_intent == "Weather" and max_similarity >= self.similarity_threshold:
            detected_location = await asyncio.to_thread(self._extract_location, query)
        
        return best_intent, round(max_similarity, 2), detected_location

