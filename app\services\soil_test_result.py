import os
import re
import json
import uuid
import base64
import tempfile
from typing import Dict, Any, Optional, Union
from datetime import datetime
from io import BytesIO
from dataclasses import dataclass

import boto3
import mimetypes
from PIL import Image
from fastapi import UploadFile, HTTPException, status
from langchain_community.vectorstores import FAISS
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import PyPDFLoader
from langchain_google_genai import ChatGoogleGenerativeAI, GoogleGenerativeAIEmbeddings

from config import secrets, logger
from utils.llm_gemini import GeminiClient
from schemas.soil_test_result import SoilTestResponse
from dotenv import load_dotenv

load_dotenv()


@dataclass
class ConversionConfig:
    """Configuration for unit conversions."""
    plow_depth_inches: float = 6
    plow_depth_cm: float = 15
    soil_bulk_density: float = 1.33


class UnitConverter:
    """Handles unit conversions for soil test parameters."""
    
    @staticmethod
    def mg_kg_to_ppm(value: float) -> float:
        """Convert mg/kg to ppm (1:1 ratio)."""
        return value

    @staticmethod
    def lbs_acre_to_ppm(value: float, config: ConversionConfig) -> float:
        """Convert lbs/acre to ppm."""
        plow_depth_cm = config.plow_depth_inches * 2.54
        soil_weight_kg = 10000 * (plow_depth_cm / 100) * config.soil_bulk_density * 1000
        soil_weight_per_acre_kg = soil_weight_kg * 0.4047
        value_kg = value * 0.4536
        return (value_kg / soil_weight_per_acre_kg) * 1e6

    @staticmethod
    def kg_ha_to_ppm(value: float, config: ConversionConfig) -> float:
        """Convert kg/ha to ppm."""
        soil_weight_kg = 10000 * (config.plow_depth_cm / 100) * config.soil_bulk_density * 1000
        return (value / soil_weight_kg) * 1e6

    @classmethod
    def convert_to_ppm(cls, value: float, unit: Optional[str], config: ConversionConfig) -> float:
        """Convert various units to ppm."""
        if unit in ("ppm", "mg/kg", None):
            return cls.mg_kg_to_ppm(value)
        elif unit == "lbs/acre":
            return cls.lbs_acre_to_ppm(value, config)
        elif unit == "kg/ha":
            return cls.kg_ha_to_ppm(value, config)
        else:
            logger.warning(f"Unknown unit {unit}, returning original value")
            return value


class FileValidator:
    """Validates and processes uploaded files."""
    
    IMAGE_MIME_TYPES = {"image/png", "image/jpeg", "image/gif", "image/bmp", "image/tiff"}
    PDF_MIME_TYPES = {"application/pdf"}
    SUPPORTED_MIME_TYPES = IMAGE_MIME_TYPES | PDF_MIME_TYPES
    
    @classmethod
    def validate_mime_type(cls, mime_type: str) -> bool:
        """Validate if the MIME type is supported."""
        return mime_type in cls.SUPPORTED_MIME_TYPES
    
    @classmethod
    def is_image(cls, mime_type: str) -> bool:
        """Check if the MIME type is an image."""
        return mime_type in cls.IMAGE_MIME_TYPES
    
    @classmethod
    def is_pdf(cls, mime_type: str) -> bool:
        """Check if the MIME type is a PDF."""
        return mime_type in cls.PDF_MIME_TYPES


class ImageProcessor:
    """Handles image processing operations."""
    
    def __init__(self, max_width: int = 800):
        self.max_width = max_width
    
    def resize_image(self, image_bytes: bytes, mime_type: str) -> BytesIO:
        """Resize image to maximum width while preserving aspect ratio."""
        try:
            img = Image.open(BytesIO(image_bytes))
            original_format = self._determine_format(img, mime_type)
            
            if img.width > self.max_width:
                img = self._resize_with_aspect_ratio(img)
            
            return self._save_image(img, original_format)
            
        except Exception as e:
            logger.error(f"Error resizing image: {e}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Error processing image"
            ) from e
    
    def _determine_format(self, img: Image.Image, mime_type: str) -> str:
        """Determine the image format."""
        original_format = img.format or mimetypes.guess_extension(mime_type, strict=False)
        if original_format and original_format.startswith("."):
            original_format = original_format[1:]
        return (original_format.upper() if original_format else "JPEG")
    
    def _resize_with_aspect_ratio(self, img: Image.Image) -> Image.Image:
        """Resize image maintaining aspect ratio."""
        ratio = self.max_width / float(img.width)
        new_height = int(float(img.height) * ratio)
        return img.resize((self.max_width, new_height), Image.Resampling.LANCZOS)
    
    def _save_image(self, img: Image.Image, original_format: str) -> BytesIO:
        """Save image to BytesIO buffer."""
        output = BytesIO()
        save_format = (original_format if original_format in ["JPEG", "PNG", "GIF", "BMP", "TIFF"] else "JPEG")
        
        if save_format == "JPEG":
            img.save(output, format=save_format, quality=85)
        else:
            img.save(output, format=save_format)
        
        output.seek(0)
        return output


class S3Manager:
    """Manages S3 operations."""
    
    def __init__(self):
        self.client = boto3.client(
            "s3",
            aws_access_key_id=secrets["AWS_ACCESS_KEY_ID"],
            aws_secret_access_key=secrets["AWS_SECRET_ACCESS_KEY"]
        )
        self.bucket_name = secrets["AWS_S3_BUCKET"]
        self.schema = secrets["DB_SCHEMA"]
    
    def upload_file(self, file_content: Union[bytes, BytesIO], file_extension: str, mime_type: str) -> str:
        """Upload file to S3 and return the S3 key."""
        try:
            s3_key = self._generate_s3_key(file_extension)
            
            if isinstance(file_content, bytes):
                file_content = BytesIO(file_content)
            
            self.client.upload_fileobj(file_content, self.bucket_name, s3_key)
            return f"{s3_key}"
            
        except Exception as e:
            logger.error(f"Error uploading file to S3: {e}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error uploading file to S3"
            ) from e
    
    def read_file_as_data_url(self, s3_key: str) -> str:
        """Read file from S3 and return as data URL."""
        try:
            s3_object = self.client.get_object(Bucket=self.bucket_name, Key=s3_key)
            file_data = s3_object["Body"].read()
            
            mime_type = self._determine_mime_type(s3_key)
            encoded_data = base64.b64encode(file_data).decode("utf-8")
            
            return f"data:{mime_type};base64,{encoded_data}"
            
        except Exception as e:
            logger.error(f"File encoding failed: {e}")
            raise HTTPException(
                status_code=500,
                detail="Error encoding file"
            ) from e
    
    def _generate_s3_key(self, file_extension: str) -> str:
        """Generate unique S3 key for file."""
        unique_filename = f"{datetime.now().strftime('%Y-%m-%d')}/{uuid.uuid4()}.{file_extension}"
        return f"{self.schema}/api-requests/uploads/llm/soil-test-report-analysis/{unique_filename}"
    
    def _determine_mime_type(self, s3_key: str) -> str:
        """Determine MIME type from S3 key."""
        mime_type, _ = mimetypes.guess_type(s3_key)
        
        if mime_type is None:
            extension = s3_key.rsplit(".", 1)[-1].lower()
            mime_type_map = {
                "pdf": "application/pdf",
                "jpg": "image/jpeg",
                "jpeg": "image/jpeg",
                "png": "image/png",
                "gif": "image/gif",
                "bmp": "image/bmp",
                "tiff": "image/tiff",
            }
            mime_type = mime_type_map.get(extension, "application/octet-stream")
        
        return mime_type


class DataExtractor:
    """Extracts soil test data from files using LLM."""
    
    PROMPT_TEMPLATE = """
    You are a precise soil test data extraction assistant. 
    Analyze the provided image or text and extract values for the specified fields, including their units (e.g., ppm, mg/kg, %, lbs/acre, kg/ha). 
    Return a valid JSON object with fields as specified below. For fields requiring units (e.g., ppm, mg/kg, lbs/acre, kg/ha, %), return as {"value": float, "unit": str}. 
    For fields with fixed units, return the value directly as a float (e.g., soil_ec in dS/m, soil_ph)
    Handle synonyms, abbreviations, or units (e.g., 'pH' or 'Soil pH', 'N' or 'Nitrogen'). For test_date, use ISO 8601 format (e.g., 2025-05-12T14:22:29Z) if found.

    Required Fields:
    - soil_ph (float, no unit needed, e.g., pH, Soil pH, ph, soil pH)
    - soil_nitrogen_ppm ({"value": float, "unit": str}, e.g., N, Nitrate, NO3-N, ppm, Nitrogen)
    - soil_potassium_ppm ({"value": float, "unit": str}, e.g., K, K2O, ppm, Potassium, potassium)
    - soil_phosphorus_ppm ({"value": float, "unit": str}, e.g., P, P2O5, ppm, Phosphorus, phosphorus)
    
    Optional Fields:
    - test_date (string, ISO 8601, e.g., 2025-05-12T14:22:29Z)
    - test_type (string, e.g., routine, baseline)
    - soil_texture (string, e.g., sandy, loam)
    - soil_moisture_pct (float, %)
    - soil_ec (float, dS/m, e.g., EC, Electrical Conductivity)
    - soil_calcium_ppm ({"value": float, "unit": str}, ppm, Calcium, calcium)
    - soil_magnesium_ppm ({"value": float, "unit": str}, ppm, Magnesium, magnesium)
    - soil_sulfur_ppm ({"value": float, "unit": str}, ppm, Sulfur, sulfur)
    - soil_organic_matter_pct (float, %, Organic Matter, OM, Organic content)
    - soil_micronutrients (dict, e.g., {"zinc": {"value": float, "unit": str}})
    """
    
    PPM_FIELDS = [
        "soil_nitrogen_ppm", "soil_phosphorus_ppm", "soil_potassium_ppm",
        "soil_calcium_ppm", "soil_magnesium_ppm", "soil_sulfur_ppm"
    ]
    
    def __init__(self, llm_client: GeminiClient):
        self.llm_client = llm_client
        self.converter = UnitConverter()
        self.conversion_config = ConversionConfig()
        self.embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
    
    def extract_from_data_url(self, data_url: str) -> Dict[str, Any]:
        """Extract soil test data from data URL."""
        try:
            mime_type = self._parse_mime_type(data_url)
            
            if FileValidator.is_pdf(mime_type):
                return self._extract_from_pdf(data_url)
            elif FileValidator.is_image(mime_type):
                return self._extract_from_image(data_url)
            else:
                raise HTTPException(
                    status_code=400,
                    detail=f"Unsupported file type: {mime_type}"
                )
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Unexpected error in data extraction: {e}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail="Error in extracting soil test data"
            ) from e
    
    def _parse_mime_type(self, data_url: str) -> str:
        """Parse MIME type from data URL."""
        match = re.match(r"data:([^;]+);base64,", data_url)
        if not match:
            logger.error(f"Invalid data URL format: {data_url[:100]}")
            raise HTTPException(status_code=400, detail="Invalid data URL format")
        return match.group(1)
    
    def _extract_from_pdf(self, data_url: str) -> Dict[str, Any]:
        """Extract data from PDF using RAG approach."""
        try:
            pdf_bytes = self._decode_pdf_data(data_url)
            
            with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp_file:
                tmp_file.write(pdf_bytes)
                tmp_file_path = tmp_file.name
            
            try:
                return self._process_pdf_with_rag(tmp_file_path)
            finally:
                if os.path.exists(tmp_file_path):
                    os.remove(tmp_file_path)
                    
        except Exception as e:
            logger.error(f"Error processing PDF: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail="Failed to process PDF") from e
    
    def _decode_pdf_data(self, data_url: str) -> bytes:
        """Decode base64 PDF data."""
        try:
            _, base64_data = data_url.split(",", 1)
            return base64.b64decode(base64_data)
        except Exception as e:
            logger.error(f"Error decoding base64 PDF data: {e}", exc_info=True)
            raise HTTPException(status_code=400, detail="Failed to decode PDF data") from e
    
    def _process_pdf_with_rag(self, pdf_path: str) -> Dict[str, Any]:
        """Process PDF using RAG (Retrieval-Augmented Generation)."""
        try:
            # Load and validate PDF
            loader = PyPDFLoader(pdf_path)
            docs = loader.load()
            
            if not docs:
                raise HTTPException(
                    status_code=400,
                    detail="No text content could be extracted from the PDF"
                )
            
            # Split text into chunks
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=1000,
                chunk_overlap=200,
                length_function=len,
                is_separator_regex=False
            )
            splits = text_splitter.split_documents(docs)
            
            if not splits:
                raise HTTPException(
                    status_code=400,
                    detail="No text chunks could be created from the PDF"
                )
            
            # Create embeddings and vectorstore
            try:
                vectorstore = FAISS.from_documents(documents=splits, embedding=self.embeddings)
            except IndexError as e:
                logger.error(f"Error creating embeddings: {str(e)}", exc_info=True)
                raise HTTPException(
                    status_code=500,
                    detail="Error processing PDF content: Could not create embeddings"
                )
            
            retriever = vectorstore.as_retriever(search_kwargs={"k": 5})
            
            # Retrieve relevant context
            context_text = self._retrieve_relevant_context(retriever)
            if not context_text:
                raise HTTPException(
                    status_code=400,
                    detail="No relevant content could be extracted from the PDF"
                )
            
            # Build and process prompt
            augmented_prompt = self._build_augmented_prompt(context_text)
            
            response = self.llm_client.prompt_chat_model(
                prompt=augmented_prompt,
                output_format="json"
            )
            
            return self._parse_and_convert_response(response)
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error processing PDF: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error processing PDF: {str(e)}"
            )
    
    def _retrieve_relevant_context(self, retriever) -> str:
        """Retrieve relevant context from vectorstore."""
        retrieval_query = """
        Extract information related to soil test parameters, including:
        - Soil pH (e.g., pH, Soil pH, ph, soil pH)
        - Nitrogen (e.g., N, Nitrate, NO3-N, Nitrogen, nitrogen)
        - Potassium (e.g., K, K2O, Potassium, potassium)
        - Phosphorus (e.g., P, P2O5, Phosphorus, phosphorus)
        - Calcium (e.g., Ca, Calcium, calcium)
        - Magnesium (e.g., Mg, Magnesium, magnesium)
        - Sulfur (e.g., S, Sulfate, SO4, Sulfur, sulfur)
        - Soil Organic Matter (e.g., Organic Matter, OM, SOM, Organic content)
        - Electrical Conductivity (e.g., EC, dS/m, soil_ec)
        - Soil Texture (e.g., sandy, loam, clay)
        - Micronutrients (e.g., zinc, iron, copper)
        Include values, units (ppm, mg/kg, %, lbs/acre, kg/ha for ppm fields; dS/m for EC), and any context if available.
        """
        
        retrieved_docs = retriever.invoke(retrieval_query)
        return "\n---\n".join([doc.page_content for doc in retrieved_docs])
    
    def _build_augmented_prompt(self, context_text: str) -> str:
        """Build augmented prompt with context."""
        return f"""
        {self.PROMPT_TEMPLATE}
        Relevant text from the document:
        {context_text}
        Now, analyze the relevant text provided above and extract the data strictly according to the Required and Optional Fields listed in the instructions. Return only the JSON object.
        """
    
    def _extract_from_image(self, data_url: str) -> Dict[str, Any]:
        """Extract data from image."""
        response = self.llm_client.prompt_with_image(
            prompt_text=self.PROMPT_TEMPLATE,
            image_paths=[data_url],
            output_format="json"
        )
        
        return self._parse_and_convert_response(response)
    
    def _parse_and_convert_response(self, response: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Parse LLM response and convert units."""
        if response is None:
            raise HTTPException(status_code=500, detail="LLM client did not return a response")
        
        try:
            if isinstance(response, str):
                extracted_data = json.loads(response)
            else:
                extracted_data = response
            
            return self._convert_units(extracted_data)
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail="Failed to parse model response as JSON"
            ) from e
    
    def _convert_units(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert units in extracted data."""
        converted_data = {}
        
        for key, value in data.items():
            if key in self.PPM_FIELDS and isinstance(value, dict) and "value" in value and "unit" in value:
                ppm = self.converter.convert_to_ppm(
                    value["value"], 
                    value["unit"], 
                    self.conversion_config
                )
                converted_data[key] = round(ppm, 1) if ppm is not None else None
                
            elif key == "soil_micronutrients" and isinstance(value, dict):
                converted_data[key] = self._convert_micronutrients(value)
            else:
                converted_data[key] = value
        
        return converted_data
    
    def _convert_micronutrients(self, micronutrients: Dict[str, Any]) -> Dict[str, Any]:
        """Convert micronutrient units."""
        converted = {}
        
        for nutrient, data in micronutrients.items():
            if isinstance(data, dict) and "value" in data and "unit" in data:
                ppm = self.converter.convert_to_ppm(
                    data["value"], 
                    data["unit"], 
                    self.conversion_config
                )
                converted[nutrient] = round(ppm, 1) if ppm is not None else None
            else:
                converted[nutrient] = data
        
        return converted


class SoilTestLLMService:
    """
    Service for soil test processing with improved structure and performance.
    """
    
    def __init__(self):
        self.llm_client = GeminiClient(
            model=secrets["GEMINI_DEFAULT_MODEL"], 
            temperature=0.0
        )
        self.s3_manager = S3Manager()
        self.image_processor = ImageProcessor()
        self.data_extractor = DataExtractor(self.llm_client)
    
    async def process_uploaded_file(self, file: UploadFile) -> Dict[str, Any]:
        """Process uploaded file and extract soil test data."""
        # Validate file type
        if not FileValidator.validate_mime_type(file.content_type):
            raise HTTPException(
                status_code=400, 
                detail="Unsupported file type"
            )
        
        # Read file content
        file_bytes = await file.read()
        await file.seek(0)
        
        # Process based on file type
        if FileValidator.is_image(file.content_type):
            processed_content = self.image_processor.resize_image(file_bytes, file.content_type)
            return self.llm_client.ask_file(processed_content.read())
        elif FileValidator.is_pdf(file.content_type):
            return self.llm_client.ask_file(file_bytes)
    
    def process_soil_test(self, file_data: UploadFile) -> SoilTestResponse:
        """
        Main processing method: upload to S3, extract data, and return results.
        """
        try:
            # Upload file to S3
            s3_key = self._upload_file_to_s3(file_data)
            # logger.info(f"Uploaded file to S3 with key: {s3_key}")
            
            # Read file from S3 as data URL
            data_url = self.s3_manager.read_file_as_data_url(s3_key)
            # logger.info(f"Read file from S3 as data URL: {data_url}")
            
            # Extract soil test data
            result_dict = self.data_extractor.extract_from_data_url(data_url)
            # logger.info(f"Extracted soil test data: {result_dict}")
            
            return SoilTestResponse(
                status="success",
                message="Soil test data analyzed and extracted successfully.",
                data=[result_dict],
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error in process_soil_test: {e}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An unexpected error occurred while processing the soil test"
            ) from e
    
    def _upload_file_to_s3(self, file: UploadFile) -> str:
        """Upload file to S3 with proper processing."""
        try:
            mime_type = file.content_type or self._guess_mime_type(file.filename)
            file_extension = self._extract_file_extension(file.filename, mime_type)
            
            # Read file content
            file.file.seek(0)
            file_content = file.file.read()
            file.file.seek(0)
            
            # Process content based on type
            if FileValidator.is_image(mime_type):
                processed_content = self.image_processor.resize_image(file_content, mime_type)
                return self.s3_manager.upload_file(processed_content, file_extension, mime_type)
            elif FileValidator.is_pdf(mime_type):
                return self.s3_manager.upload_file(file_content, file_extension, mime_type)
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Unsupported file type: {mime_type}"
                )
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error uploading file: {e}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error uploading file"
            ) from e
    
    def _guess_mime_type(self, filename: Optional[str]) -> str:
        """Guess MIME type from filename."""
        if not filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Could not determine file MIME type"
            )
        
        mime_type, _ = mimetypes.guess_type(filename)
        if not mime_type:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Could not determine file MIME type"
            )
        
        return mime_type
    
    def _extract_file_extension(self, filename: Optional[str], mime_type: str) -> str:
        """Extract file extension from filename or MIME type."""
        if filename:
            name_parts = filename.rsplit(".", 1)
            if len(name_parts) > 1:
                return name_parts[-1]
        
        # Fallback to MIME type
        guessed_extension = mimetypes.guess_extension(mime_type)
        return guessed_extension[1:] if guessed_extension else "bin"
    