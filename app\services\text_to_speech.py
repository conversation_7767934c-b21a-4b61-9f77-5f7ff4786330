import os
import json
import base64
import requests
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, status

from io import BytesIO
from schemas.text_to_speech import TextToSpeechRequest
from fastapi.responses import StreamingResponse

from config import secrets, logger


class GoogleTTS:
    """
    A class to interact with Google's Text-to-Speech REST API.

    This client handles the construction of requests, making the API call,
    and processing the response to save the audio to a file.
    """
    # The base URL for the Text-to-Speech API endpoint.
    API_KEY = secrets["GOOGLE_TEXT_TO_SPEECH_API_KEY"]
    API_ENDPOINT = "https://texttospeech.googleapis.com/v1/text:synthesize"

    def __init__(self):
        """
        Initializes the GoogleTTS client.

        Args:
            api_key (str): Your Google Cloud API key for authentication.
        """
        self.url = f"{self.API_ENDPOINT}?key={self.API_KEY}"

    def synthesize(self, 
                   text: str, 
                   voice_name: str = "en-US-Wavenet-C", 
                   language_code: str = "en-US",
                   speaking_rate: float = 1.0,
                   pitch: float = 0.0):
        """
        Synthesizes the given text and saves it as an MP3 file.

        Args:
            text (str): The text to be converted to speech.
            output_filename (str): The path to save the output MP3 file.
            voice_name (str): The name of the voice to use (e.g., "en-US-Wavenet-D").
            language_code (str): The language code (e.g., "en-US").
            speaking_rate (float): Speaking rate/speed, from 0.25 to 4.0. 1.0 is the default.
            pitch (float): Speaking pitch in semitones, from -20.0 to 20.0. 0.0 is the default.

        Returns:
            bool: True if synthesis was successful, False otherwise.
        """
        request_body = {
            "input": {
                "text": text
            },
            "voice": {
                "languageCode": language_code,
                "name": voice_name
            },
            "audioConfig": {
                "audioEncoding": "MP3",
                "speakingRate": speaking_rate,
                "pitch": pitch
            }
        }

        headers = {
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.post(self.url,
                                     data=json.dumps(request_body), 
                                     headers=headers)
            response.raise_for_status()  
            response_json = response.json()
            if 'audioContent' not in response_json:
                logger.error("Error: 'audioContent' not found in response.")
                return False

            audio_content = base64.b64decode(response_json['audioContent'])
            audio_stream = BytesIO(audio_content)
            return audio_stream
        
        except requests.exceptions.HTTPError as http_err:
            logger.error(f"HTTP error occurred: {http_err}")
        except Exception as err:
            logger.error(f"An error occurred: {err}")
        return None    
    

class TextToSpeechService:
    def __init__(self):
        self.google_tts = GoogleTTS()

    def get_speech_audio(self, data: TextToSpeechRequest):
        try:
            audio_stream = self.google_tts.synthesize(**data)
            return StreamingResponse(
                audio_stream,
                media_type="audio/mpeg",
                headers={
                    "Content-Disposition": "inline; filename=output.mp3"
                }
            )
        except Exception as e:
            logger.error(f"Error synthesizing speech: {e}")
            raise HTTPException(
                status_code=500, 
                detail="Error synthesizing speech"
            )
    