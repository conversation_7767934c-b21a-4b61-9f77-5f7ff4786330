import os
import logging
from typing import Annotated
from contextlib import asynccontextmanager
from fastapi import APIRouter, HTTPException, Depends, status, Request, UploadFile, File, FastAPI

from schemas.users import User
import schemas.advisory_rag_system as schema
import schemas.intent_classification as ic_schema
import schemas.text_to_speech as tts_schema

from services.api_log import ApiLogsService
from services.auth import get_current_active_user
from services.rag_services.core_rag_service import RAGService, get_rag_service
from services.text_to_speech import TextToSpeechService
from services.intent_classifier import IntentClassifierService
from services.rag_services.rag_config import Settings
from services.rag_services import core_rag_service

logger = logging.getLogger(__name__)  # Add logger
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"

router = APIRouter()
current_user = Depends(get_current_active_user)

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    settings = Settings()
    core_rag_service.rag_service_instance = RAGService(settings)
    
    yield
    
    # Shutdown
    if core_rag_service.rag_service_instance:
        core_rag_service.rag_service_instance.cleanup()

@router.post(
    "/farm-advisory-query",
    response_model=schema.RagQueryResponse,
    summary="Query the RAG system with a question using a specified LLM provider",
)
async def query_rag(
    data: schema.RagQueryRequest,
    current_user: Annotated[User, current_user],
    rag_service: RAGService = Depends(get_rag_service),
) -> schema.RagQueryResponse:
    """
    Query the RAG system with a question using a specified LLM provider.
    """
    
    response = rag_service.query(
        question=data.question, 
        llm_provider=data.llm_provider, 
        rag=data.rag,
        user_id=current_user.user_id, 
        farm_context=data.farm_context,     
        channel_id=data.channel_id,
        language=data.language,
        conversation_id=data.conversation_id,
    )

    if not isinstance(response, schema.RagQueryResponse):
        response = schema.RagQueryResponse(**response)

    return response


@router.post(
    "/response-feedback",
    response_model=schema.MessageFeedbackResponse,
    summary="Provide feedback on a message in a conversation",
)
async def message_feedback(
    data: schema.MessageFeedbackRequest,
    current_user: Annotated[User, current_user],
    rag_service: RAGService = Depends(get_rag_service),
) -> schema.MessageFeedbackResponse:
    """
    Provide feedback on a message in a conversation.
    
    Args:
        data: The feedback request containing message_id and feedback type. 
              conversation_id and channel_id are optional and will be retrieved from the message.
        current_user: The authenticated user
        rag_service: The RAG service instance
        
    Returns:
        MessageFeedbackResponse: The result of the feedback operation
        
    Raises:
        HTTPException: If the RAG service is not ready or if the feedback operation fails
    """
    
    response = rag_service.conversation_memory.feedback_message(
        message_id=data.message_id,
        feedback=data.feedback
    )

    if not isinstance(response, schema.MessageFeedbackResponse):
        response = schema.MessageFeedbackResponse(**response)
    
    return response


@router.post(
    "/clear-conversation",
    response_model=schema.ClearConversationResponse,
    summary="Clear the conversation history for a user",
)
async def clear_conversation(
    data: schema.ClearConversationRequest,
    current_user: Annotated[User, current_user],
    rag_service: RAGService = Depends(get_rag_service),
) -> schema.ClearConversationResponse:
    """
    Clear the conversation history for a specific user.
    This will remove all previous context for future queries.
    """
    
    response = rag_service.clear_conversation(data.conversation_id)
    if not isinstance(response, schema.ClearConversationResponse):
        response = schema.ClearConversationResponse(**response)
        
    return response


@router.post(
    "/create-conversation",
    response_model=schema.CreateConversationResponse,
    summary="Create a new conversation for a user",
)
async def create_conversation(
    data: schema.CreateConversationRequest,
    current_user: Annotated[User, current_user],
    rag_service: RAGService = Depends(get_rag_service),
) -> schema.CreateConversationResponse:
    """
    Create a new conversation for a user.
    This will create a new conversation and deactivate any existing active conversations.
    
    Args:
        data: The request containing user_id, channel_id, and optional title
        current_user: The authenticated user
        rag_service: The RAG service instance
        
    Returns:
        CreateConversationResponse: The created conversation details
        
    Raises:
        HTTPException: If the RAG service is not ready or if conversation creation fails
    """
    
    try:
        response = rag_service.create_conversation(
            user_id=current_user.user_id,
            channel_id=data.channel_id,
            title=data.title
        )
        
        if not isinstance(response, schema.CreateConversationResponse):
            response = schema.CreateConversationResponse(**response)
            
        return response
        
    except Exception as e:
        logger.error(f"Error creating conversation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create conversation: {str(e)}"
        )


@router.post(
    "/intent-classify", 
    response_model=ic_schema.IntentClassificationOutput,
    summary="Classify a user query into predefined intents (Weather, Diagnoses, Farm Actions or General)"
)
async def classify_user_query(
    data: ic_schema.IntentClassificationInput,
    current_user: Annotated[User, current_user], 
    request: Request
):
    """Get query intent"""
    return IntentClassifierService().classify_query(data.query)


@router.post(
    "/text-to-speech",
    response_model=tts_schema.TextToSpeechResponse,
    summary="Convert text to speech"
)
async def text_to_speech(
    data: tts_schema.TextToSpeechRequest,
    current_user: Annotated[User, current_user],
    request: Request
):
    """Convert text to speech and stream the audio as MP3."""
    return TextToSpeechService().get_speech_audio(data.dict())


@router.get(
    "/conversation-history/{conversation_id}",
    response_model=schema.ConversationHistoryResponse,
    summary="Get the conversation history for a user",
)
async def get_conversation_history(
    conversation_id: str,
    current_user: Annotated[User, current_user],
    rag_service: RAGService = Depends(get_rag_service),
) -> schema.ConversationHistoryResponse:
    """
    Get the conversation history for a specific user.
    """
    history = rag_service.get_conversation_history(conversation_id)
    return schema.ConversationHistoryResponse(
        conversation_id=conversation_id, history=history
    )


@router.get(
    "/conversations/user/{user_id}",
    response_model=schema.UserConversationsResponse,
    summary="Get all conversations for a user",
)
async def get_conversations_by_user_id(
    user_id: str,
    current_user: Annotated[User, current_user],
    rag_service: RAGService = Depends(get_rag_service),
) -> schema.UserConversationsResponse:
    """
    Get all conversations for a specific user.
    """
    conversations = rag_service.get_conversations_by_user_id(user_id)
    return schema.UserConversationsResponse(
        user_id=user_id, conversations=conversations
    )
