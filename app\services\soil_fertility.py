import boto3
import urllib.request
import joblib
import threading
import langchain
from botocore.exceptions import Client<PERSON>rror
from typing import Dict, Any
from fastapi import HTTPException, status
from pydantic import ValidationError
from langchain_core.prompts import ChatPromptTemplate

from utils.llm_openai import OpenAIClient
from config import secrets, logger
import schemas.soil_fertility as schema

# config
langchain.verbose = False
langchain.debug = False
langchain.llm_cache = False


class SoilFertilityMLService:
    """Service for making soil fertility predictions using a trained model stored in S3."""

    MODEL_S3_PATH_TEMPLATE = "{env}/models/soil-fertility/random_forest_v1.pkl"
    PRESIGNED_URL_EXPIRATION = 3600  # 1 hour
    DEFAULT_LABEL_MAPPING = {
        0: "low",
        1: "medium",
        2: "high"
    }

    # Class-level variables for caching
    _model_instance = None
    _model = None
    _label_mapping = None
    _model_loaded = False
    _lock = threading.Lock()  # Add thread safety for model loading

    def __new__(cls):
        """Implement singleton pattern to ensure only one instance exists."""
        if cls._model_instance is None:
            cls._model_instance = super(SoilFertilityMLService, cls).__new__(cls)
        return cls._model_instance

    def __init__(self):
        """Initialize the service with AWS credentials and configuration."""
        # Skip reinitialization if already initialized
        if hasattr(self, '_initialized'):
            return

        try:
            self.s3 = boto3.client(
                's3',
                aws_access_key_id=secrets['AWS_ACCESS_KEY_ID'],
                aws_secret_access_key=secrets["AWS_SECRET_ACCESS_KEY"],
                region_name=secrets["AWS_REGION"]
            )
            self.bucket_name = secrets["AWS_S3_BUCKET"]
            self.s3_key = self.MODEL_S3_PATH_TEMPLATE.format(env=secrets["ENV"])
            self._initialized = True  # Mark as initialized
        except KeyError as e:
            logger.error(f"Missing required configuration: {e}")
            raise RuntimeError(f"Missing required configuration: {e}")
        except Exception as e:
            logger.error(f"Failed to initialize service: {e}")
            raise RuntimeError(f"Service initialization failed: {e}")

    def get_presigned_url(self, expiration: int = PRESIGNED_URL_EXPIRATION) -> str:
        """Generate a presigned URL to share the S3 object."""
        try:
            url = self.s3.generate_presigned_url(
                'get_object',
                Params={'Bucket': self.bucket_name, 'Key': self.s3_key},
                ExpiresIn=expiration
            )
            return url
        except ClientError as e:
            logger.error(f"Error generating presigned URL: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Could not generate presigned URL for model download."
            )

    def _load_model(self) -> None:
        """Load the model from S3 if not already loaded."""
        if self._model is not None:
            return
 
        try:
            url = self.get_presigned_url()
            with urllib.request.urlopen(url) as response:
                model_data = joblib.load(response)
            return model_data
        except Exception as e:
            logger.error(f"Error loading model from URL: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Could not load model from URL."
            )

    def _load_model(self) -> None:
        """Load the model from S3 if not already loaded (thread-safe)."""
        if not self._model_loaded:
            with self._lock:
                if not self._model_loaded:  # Double-check locking pattern
                    url = self.get_presigned_url()
                    model_data = self.load_model_from_url(url)
                    self.__class__._model = model_data['model']
                    self.__class__._label_mapping = model_data.get(
                        'label_mapping',
                        self.DEFAULT_LABEL_MAPPING
                    )
                    self.__class__._model_loaded = True

    @staticmethod
    def _convert_rating_label(rating: str) -> str:
        """Convert model legacy rating labels to standardized labels."""
        conversion_map = {
            "less fertile": "low",
            "fertile": "medium",
            "highly fertile": "high"
        }
        return conversion_map.get(rating, "unknown")

    def get_soil_fertility_prediction(self, data: schema.SoilTestData) -> schema.SoilFertility:
        """Make a soil fertility prediction based on input test data."""
        try:
            # Convert input to 2D array
            input_array = [[
                data.nitrogen,
                data.phosphorus,
                data.potassium,
                data.soil_pH,
                data.organic_carbon
            ]]

            # Model is already loaded during initialization, no need to check here
            prediction = self._model.predict(input_array)[0]
            rating = self._label_mapping.get(prediction, "unknown")

            if rating in {"less fertile", "fertile", "highly fertile"}:
                rating = self._convert_rating_label(rating)

            return schema.SoilFertility(
                rating=rating,
                score=None,
                explanation=None
            )
        except ValidationError as e:
            logger.error(f"Output validation error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error validating prediction output."
            )
        except Exception as e:
            logger.error(f"Prediction error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred during prediction."
            )


class SoilFertilityLLMService:
    llm = OpenAIClient(
        model=secrets['OPENAI_DEFAULT_MODEL'],
        temperature=0.0
    )

    def soil_fertility_llm_prompt(self, data: schema.SoilTestData):
        """Prompt the LLM with soil fertility data"""
        prompt_text = """
        You are an agronomic expert. I have the following soil data:

        Soil Texture: {soil_texture}
        soil pH: {soil_ph}
        Nitrogen (%): {soil_nitrogen_ppm}
        Phosphorus (%): {soil_phosphorus_ppm}
        Potassium (%): {soil_potassium_ppm}
        Soil Moisture (%): {soil_moisture_pct}
        Organic Matter (%): {soil_organic_matter_pct}
        Temperature (°C): {soil_temperature}

        1. Based on these parameters, provide a concise agronomic interpretation.
        2. Classify the overall soil fertility into one of three categories: Low, Medium, or High.
        3. Give me a fertility score between 1 and 100, where 1 is extremely infertile and 100 is extremely fertile.

        Take into account typical thresholds for pH, nutrients (N, P, K, organic matter), and the effects of rainfall, temperature, and soil type on nutrient availability.

        Return the response in JSON format using the below structure:
        {{
            "rating": "Medium",
            "score": 50,
            "explanation": "The soil is moderately fertile with a balanced pH and nutrient profile. It is suitable for a wide range of crops."
        }}
        """
        messages = [
            ("system", "Determining soil fertility..."),
            ("human", prompt_text)
        ]
        prompt = ChatPromptTemplate(messages).invoke(
            {
                "soil_texture": data.soil_texture,
                "soil_ph": data.soil_ph,
                "soil_nitrogen_ppm": data.soil_nitrogen_ppm,
                "soil_phosphorus_ppm": data.soil_phosphorus_ppm,
                "soil_potassium_ppm": data.soil_potassium_ppm,
                "soil_moisture_pct": data.soil_moisture_pct,
                "soil_organic_matter_pct": data.soil_organic_matter_pct,
                "soil_temperature": data.soil_temperature
            }
        )
        return self.llm.prompt_chat_model(prompt)

    def get_soil_fertility(self, data: schema.SoilTestData):
        """Get soil fertility rating and score"""
        try:
            # Prompt LLM
            response = self.soil_fertility_llm_prompt(data)

            # Parse response
            score = response['score'] / 100
            return schema.SoilFertility(
                rating=response['rating'].lower(),
                score=score,
                explanation=response['explanation']
            )
        except Exception as e:
            logger.error(f"Error getting soil fertility rating: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error getting soil fertility rating"
            )
