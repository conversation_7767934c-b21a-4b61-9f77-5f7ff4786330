from langchain_core.prompts import ChatPromptTemplate
from fastapi import HTTPException, status

from config import secrets, logger
from utils.llm_openai import OpenAIClient

import schemas.soil_test_recommendations as schema
import schemas.soil_fertility as schema_soil_fertility
import schemas.crop_recommendations as schema_crop_recommendations

from services.soil_fertility import SoilFertilityLLMService
from services.crop_recommendations import CropRecommendationLLMService


class SoilTestRecommendationMLService:
    pass


class SoilTestRecommendationLLMService:
    def __init__(self):
        self.llm = OpenAIClient(
            model=secrets['OPENAI_DEFAULT_MODEL'], 
            temperature=0.0
        )
        self.soil_fertility_service = SoilFertilityLLMService()
        self.crop_recommendation_service = CropRecommendationLLMService()
        self.optimal_ranges = {
            'soil_ec': (0.2, 1.5),
            'soil_ph': (5.0, 7.2),
            'target_soil_ph': 6.8,
            'soil_moisture_pct': (10, 30),
            'soil_temperature': (15, 30),
            'soil_nitrogen_ppm': (10, 20),
            'soil_phosphorus_ppm': (40, 60),
            'soil_potassium_ppm': (100, 160),
            'soil_calcium_ppm': (700, 1100),
            'soil_magnesium_ppm': (100, 190),
            'soil_sulfur_ppm': (9, 25),
            'soil_organic_matter_pct': (3, 5)
        }

    def get_soil_test_recommendation(self, soil_test_data: schema.SoilTestData) -> schema.SoilTestRecommendation:
        """Get soil test recommendation"""
        try:
            # Get soil fertility analysis
            data = schema_soil_fertility.SoilTestData(
                soil_texture=soil_test_data.soil_texture,
                soil_ph=soil_test_data.soil_ph,
                soil_nitrogen_ppm=soil_test_data.soil_nitrogen_ppm,
                soil_phosphorus_ppm=soil_test_data.soil_phosphorus_ppm,
                soil_potassium_ppm=soil_test_data.soil_potassium_ppm,
                soil_moisture_pct=soil_test_data.soil_moisture_pct,
                soil_organic_matter_pct=soil_test_data.soil_organic_matter_pct,
                soil_temperature=soil_test_data.soil_temperature
            )
            soil_fertility = self.soil_fertility_service.get_soil_fertility(data)

            # Get crop recommendations
            data = schema_crop_recommendations.SoilTestData(
                soil_texture=soil_test_data.soil_texture,
                soil_ph=soil_test_data.soil_ph,
                soil_nitrogen_ppm=soil_test_data.soil_nitrogen_ppm,
                soil_phosphorus_ppm=soil_test_data.soil_phosphorus_ppm,
                soil_potassium_ppm=soil_test_data.soil_potassium_ppm,
                soil_moisture_pct=soil_test_data.soil_moisture_pct,
                soil_organic_matter_pct=soil_test_data.soil_organic_matter_pct,
                soil_temperature=soil_test_data.soil_temperature,
                country=soil_test_data.country
            )
            crop_recommendations = self.crop_recommendation_service.get_crop_recommendations(data, 10)
            
            prompt_text = """
            You are an agronomy expert with specializations in soil test analysis. Based on the following soil test data, provide a comprehensive recommendation for soil amendment and fertilization.

            Soil Test Data:
            {soil_test_data}
            
            Optimal Ranges:
            {optimal_ranges}

            Return a detailed recommendation in JSON format with the following fields:
            - soil_amendments: List of recommended soil amendments
            - fertilizer_recommendations: List of recommended fertilizers
            - application_rates: List of recommended application rates
            - application_methods: List of recommended application methods
            - timing: Recommended timing for application
            - soil_nutrient_ratings: Dictionary with nutrient ratings based on the optimal ranges provided. The ratings should be a string ranging from very low, low, moderate, high, very high & unknown.
                - pH: Rating for soil pH
                - N: Rating for nitrogen in ppm
                - P: Rating for phosphorus in ppm
                - K: Rating for potassium in ppm
                - Ca: Rating for calcium in ppm
                - Mg: Rating for magnesium in ppm
                - S: Rating for sulfur in ppm        
            - additional_notes: Any additional notes or considerations (not a List)
            
            Ensure that the recommendation is tailored to the specific soil test data provided and aligns with the soil fertility analysis and crop recommendations.
            """
            messages = [
                ("system", "Determining soil test recommendation..."),
                ("human", prompt_text)
            ]
            prompt = ChatPromptTemplate.from_messages(messages).invoke(
                {
                    "soil_test_data": soil_test_data.json(),
                    "optimal_ranges": self.optimal_ranges
                }
            )
            response = self.llm.prompt_chat_model(prompt)

            # add soil_fertility and crop_recommendations to response
            response['soil_fertility'] = {
                "rating": soil_fertility.rating,
                "score": soil_fertility.score,
                "explanation": soil_fertility.explanation
            }
            response['crop_recommendations'] = [
                {
                    "crop_id": rec.crop_id,
                    "crop_type": rec.crop_name,
                    "suitability_score": rec.suitability_score,
                    "explanation": rec.explanation
                } for rec in crop_recommendations.recommendations
            ]
            # response['soil_fertilizer'] = response.get('fertilizer_recommendations', [])
            
            if not isinstance(response, schema.SoilTestRecommendation):
                response = schema.SoilTestRecommendation(**response)
            
            return response
            
        except Exception as e:
            logger.error(f"Error getting soil test recommendation: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error getting soil test recommendation"
            )
    