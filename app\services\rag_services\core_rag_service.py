import time
from fastapi import Request
from datetime import timedelta, datetime, timezone
from typing import Dict, Any, Optional, List, TypedDict
from fastapi import Request, HTTPException, status
from dateutil import parser

import uuid
import pymongo
from pymongo.collection import Collection
from pymongo import MongoClient

from langchain.schema.retriever import BaseR<PERSON>riever
from langchain_core.documents import Document
from langchain_core.runnables import RunnablePassthrough, RunnableParallel
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.language_models.chat_models import BaseChatModel

# langchain llm clients imports
from langchain_google_genai import Chat<PERSON>oogleGenerativeAI
from langchain_groq import ChatGroq
from langchain_openai import ChatOpenAI
from langchain_deepseek import ChatDeepSeek


from config import logger, secrets
from schemas.advisory_rag_system import RagQueryResponse, Conversation, Message, MessageFeedbackResponse

from services.rag_services.rag_config import Settings
from services.rag_services.vectorstore_manager_service import VectorStoreManager
from services.weather import WeatherService


class ConversationMemoryService:
    """
    Service to manage conversation memory using MongoDB.
    Conversations are stored per user and channel, with messages stored separately.
    """

    MONGO_CONNECTION_STRING: str = secrets["MONGODB_URI"]
    DATABASE_NAME: str = "cropsense-dev" if secrets["ENV"] == "dev" else "cropsense-prod"
    COLLECTION_NAME: str = "cropsense-ai-chat-history"

    def __init__(self):
        """Initializes the MongoDB connection and selects the collections."""
        try:
            self.client: MongoClient = MongoClient(self.MONGO_CONNECTION_STRING)
            self.db = self.client[self.DATABASE_NAME]
            self.conversation_collection: Collection = self.db[f"{self.COLLECTION_NAME}-conversations"]
            self.message_collection: Collection = self.db[f"{self.COLLECTION_NAME}-messages"]

            logger.info(f"Connected to MongoDB database: {self.DATABASE_NAME}")

            # Create indexes for conversations
            self.conversation_collection.create_index([
                ("user_id", pymongo.ASCENDING),
                ("channel_id", pymongo.ASCENDING),
                ("is_active", pymongo.ASCENDING),
                ("last_updated", pymongo.DESCENDING)
            ])

            # Create indexes for messages
            self.message_collection.create_index([
                ("conversation_id", pymongo.ASCENDING),
                ("timestamp", pymongo.ASCENDING)
            ])
            
            # Create index on message_id for efficient feedback lookups
            self.message_collection.create_index([
                ("message_id", pymongo.ASCENDING)
            ])

        except pymongo.errors.ConnectionFailure as e:
            logger.error(f"Could not connect to MongoDB: {e}")
            raise
        except pymongo.errors.ConfigurationError as e:
            logger.error(f"Configuration error with MongoDB: {e}")
            raise
        except pymongo.errors.OperationFailure as e:
            logger.error(f"Operation failure during MongoDB initialization: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error initializing MongoDB connection: {e}")
            raise

    def _normalize_timestamp(self, ts: datetime | str | int | float | None) -> datetime | None:
        """
        Holistically normalizes a timestamp to a timezone-aware datetime object in UTC.
        
        Handles:
        - datetime objects (naive or aware)
        - Numeric Unix timestamps (integers or floats)
        - A wide variety of string formats (ISO 8601, common date/time strings)
        """
        if ts is None:
            return None

        if isinstance(ts, datetime):
            if ts.tzinfo is None:
                return ts.replace(tzinfo=timezone.utc)
            else:
                return ts.astimezone(timezone.utc)

        if isinstance(ts, (int, float)):
            try:
                return datetime.fromtimestamp(ts, tz=timezone.utc)
            except (ValueError, OSError) as e:
                logger.error(f"Invalid Unix timestamp value: {ts}. Error: {e}")
                return None

        if isinstance(ts, str):
            try:
                dt_obj = parser.parse(ts)
                return self._normalize_timestamp(dt_obj)
            except (parser.ParserError, ValueError):
                logger.error(f"Could not parse timestamp string: '{ts}'")
                return None

    def get_active_conversation(self, user_id: str, channel_id: str) -> Optional[Conversation]:
        """
        Get the active conversation for a user in a specific channel.
        Returns None if no active conversation exists.
        """
        try:
            doc = self.conversation_collection.find_one(
                {
                    "user_id": user_id,
                    "channel_id": channel_id,
                    "is_active": True
                },
                sort=[("last_updated", pymongo.DESCENDING)]
            )
            return Conversation(**doc) if doc else None
        except Exception as e:
            logger.error(f"Error getting active conversation: {e}")
            return None

    def create_conversation(self, user_id: str, channel_id: str, title: Optional[str] = None, first_question: Optional[str] = None) -> Optional[Conversation]:
        """
        Create a new conversation and deactivate any existing active conversations.
        Returns the new conversation if successful, None otherwise.
        
        Args:
            user_id: The user's ID
            channel_id: The channel ID
            title: Optional title for the conversation
            first_question: Optional first question to generate title from
        """
        try:
      
            # Deactivate any existing active conversations
            self.conversation_collection.update_many(
                {
                    "user_id": user_id,
                    "channel_id": channel_id,
                    "is_active": True
                },
                {"$set": {"is_active": False}}
            )
        
            # Create new conversation
            conversation = Conversation(
                user_id=user_id,
                channel_id=channel_id,
                
                title=title,
                is_active=True
            )
           
            result = self.conversation_collection.insert_one(conversation.model_dump())
            if result.inserted_id:
                logger.info(f"Created new conversation {conversation.conversation_id} for user {user_id}")
                return conversation
            return None
        except Exception as e:
            logger.error(f"Error creating conversation: {e}")
            return None

    def get_or_create_conversation(self, user_id: str, channel_id: str) -> Conversation:
        """
        Get the active conversation or create a new one if none exists.
        """
        conversation = self.get_active_conversation(user_id, channel_id)
        if not conversation:
            conversation = self.create_conversation(user_id, channel_id)
            if not conversation:
                raise Exception("Failed to create new conversation")
        return conversation

    @staticmethod
    def _generate_conversation_title(message_content: str) -> str:
        """
        Generate a short, descriptive title (max 5 words) for a conversation based on the first message content.
        This is a simplified version for ConversationMemoryService.
        """
        # Take the first 5 words, capitalize, and join as a title
        words = message_content.strip().split()
        title = " ".join(words[:5]).strip().title()
        if len(title) > 50:
            title = title[:47] + "..."
        return title or "New Conversation"

    def add_message(self, conversation_id: str, role: str, content: str) -> Optional[Message]:
        """
        Add a message to a conversation.
        Returns the created message if successful, None otherwise.
        """
        try:
            # Verify conversation exists
            conversation = self.conversation_collection.find_one({"conversation_id": conversation_id})
            if not conversation:
                logger.error(f"Conversation {conversation_id} not found")
                return None

            # Create and save message
            message = Message(
                conversation_id=conversation_id,
                role=role,
                content=content
            )
            
            result = self.message_collection.insert_one(message.model_dump())
            if result.inserted_id:
                # Update conversation's last_updated
                self.conversation_collection.update_one(
                    {"conversation_id": conversation_id},
                    {"$set": {"last_updated": message.timestamp}}
                )
                # If conversation title is None or empty, set it from the first message
                if not conversation.get("title") and role == "user":
                    generated_title = self._generate_conversation_title(content)
                    self.conversation_collection.update_one(
                        {"conversation_id": conversation_id},
                        {"$set": {"title": generated_title}}
                    )
                logger.info(f"Added message to conversation {conversation_id}")
                return message
            return None
        except Exception as e:
            logger.error(f"Error adding message: {e}")
            return None

    def get_recent_messages(self, conversation_id: str, limit: int = 5) -> List[Message]:
        """
        Get the most recent messages from a conversation.
        """
        try:
            cursor = self.message_collection.find(
                {"conversation_id": conversation_id},
                sort=[("timestamp", pymongo.DESCENDING)],
                limit=limit
            )
            return [Message(**doc) for doc in cursor]
        except Exception as e:
            logger.error(f"Error getting recent messages: {e}")
            return []

    def get_conversation_history(self, conversation_id: str) -> List[Message]:
        """
        Get the entire message history for a conversation.
        """
        try:
            cursor = self.message_collection.find(
                {"conversation_id": conversation_id},
                sort=[("timestamp", pymongo.ASCENDING)]
            )
            return [Message(**doc) for doc in cursor]
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return []

    def get_conversations_by_user_id(self, user_id: str) -> List[Conversation]:
        """
        Get all conversations for a user.
        """
        try:
            cursor = self.conversation_collection.find(
                {"user_id": user_id},
                sort=[("last_updated", pymongo.DESCENDING)]
            )
            return [Conversation(**doc) for doc in cursor]
        except Exception as e:
            logger.error(f"Error getting conversations by user ID: {e}")
            return []

    def to_context_string(self, conversation_id: str, window_size: int = 5) -> str:
        """
        Convert recent messages to a context string for the prompt.
        """
        try:
            messages = self.get_recent_messages(conversation_id, window_size)
            if not messages:
                return ""

            context = "Previous conversation:\n"
            for msg in reversed(messages):  # Reverse to show in chronological order
                role_str = str(msg.role).capitalize() if msg.role else "Unknown"
                context += f"{role_str}: {msg.content}\n"
            return context
        except Exception as e:
            logger.error(f"Error creating context string: {e}")
            return ""

    def get_context(self, conversation_id: str, window_size: int = 5) -> str:
        """
        Alias for to_context_string. Get the conversation context.
        """
        return self.to_context_string(conversation_id, window_size)

    def clear_conversation(self, conversation_id: str) -> bool:
        """
        Clear a conversation by removing all its messages.
        Returns True if successful, False otherwise.
        """
        try:
            # Delete all messages
            result = self.message_collection.delete_many({"conversation_id": conversation_id})
            
            # Update conversation
            self.conversation_collection.update_one(
                {"conversation_id": conversation_id},
                {"$set": {"last_updated": datetime.now(timezone.utc)}}
            )
            
            logger.info(f"Cleared conversation {conversation_id} ({result.deleted_count} messages deleted)")
            return True
        except Exception as e:
            logger.error(f"Error clearing conversation: {e}")
            return False

    def feedback_message(self, message_id: str, feedback: str) -> MessageFeedbackResponse:
        """
        Provide feedback on a message using only message_id.
        Returns MessageFeedbackResponse if successful, raises HTTPException otherwise.
        
        Args:
            message_id: The unique ID of the message to provide feedback on
            feedback: The feedback type ("like" or "dislike")
            
        Returns:
            MessageFeedbackResponse: The result of the feedback operation
            
        Raises:
            HTTPException: If the message is not found or feedback update fails
        """
        try:
            # First, find the message to get its conversation_id
            message_doc = self.message_collection.find_one({"message_id": message_id})
            if not message_doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Message with ID {message_id} not found"
                )
            
            conversation_id = message_doc.get("conversation_id")
            
            # Prepare the update operation
            update = {"$set": {}}
            if feedback == "like":
                update["$set"]["is_liked"] = True
                update["$set"]["is_disliked"] = False
            elif feedback == "dislike":
                update["$set"]["is_liked"] = False
                update["$set"]["is_disliked"] = True
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Feedback must be either 'like' or 'dislike'"
                )
            
            # Update the message
            result = self.message_collection.update_one(
                {"message_id": message_id},
                update
            )
            
            if result.modified_count == 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to update message feedback"
                )
            
            return MessageFeedbackResponse(
                success=True,
                message_id=message_id,
                conversation_id=conversation_id,
                feedback=feedback
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating message feedback: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error while updating feedback: {str(e)}"
            )

    def close_connection(self):
        """Close the MongoDB connection."""
        try:
            if hasattr(self, 'client') and self.client:
                self.client.close()
                logger.info("MongoDB connection closed.")
        except Exception as e:
            logger.error(f"Error closing MongoDB connection: {e}")


class RAGService:
    """
    The main service handling RAG initialization and querying.
    Context is always retrieved from the 'text' source (vector store).
    The LLM provider and model used for generation are determined per query,
    respecting the defaults and overrides defined in the Settings.
    """
    def __init__(self, settings: Settings):
        self.settings = settings
        self.context_window_size = settings.CONTEXT_WINDOW_SIZE
        self.conversation_memory = ConversationMemoryService()
        self.api_keys = {
            "gemini": self.settings.GEMINI_API_KEY,
            "llama": self.settings.GROQ_API_KEY,  # Groq hosts Llama models via 'llama' provider key
            "openai": self.settings.OPENAI_API_KEY,
            "deepseek": self.settings.DEEPSEEK_API_KEY
        }
        self.text_retriever: Optional[BaseRetriever] = None
        self.is_initialized: bool = False
        logger.info("RAGService Instantiated. Call initialize() to load data and retrievers.")

    def _get_llm_client(self, provider: str) -> BaseChatModel:
        """
        Initializes and returns an LLM client for the specified provider.
        
        Args:
            provider: The LLM provider to use (gemini, llama, openai, deepseek)
            
        Returns:
            BaseChatModel: The initialized LLM client
            
        Raises:
            HTTPException: If the provider is unsupported or API key is missing
        """
        provider = provider.lower()
        api_key = self.api_keys.get(provider)
        temp = self.settings.LLM_TEMPERATURE

        if not api_key or provider not in self.api_keys:
            logger.error(f"❌ Unsupported LLM provider '{provider}' or missing API key.")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported LLM provider '{provider}' or missing API key."
            )

        model = self.settings.LLM_MODEL if provider == self.settings.LLM_PROVIDER \
            else self.settings.LLM_MODELS_MAPPER.get(provider, self.settings.LLM_MODELS_MAPPER["deepseek"])

        llm_clients_mapping = {
            "gemini": lambda: ChatGoogleGenerativeAI(
                model=model,
                temperature=temp,
                google_api_key=api_key
            ),
            "llama": lambda: ChatGroq(model=model, temperature=temp),
            "openai": lambda: ChatOpenAI(model=model, temperature=temp),
            "deepseek": lambda: ChatDeepSeek(model=model, temperature=temp)
        }

        client_factory = llm_clients_mapping.get(provider)
        if not client_factory:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported LLM provider '{provider}'"
            )

        return client_factory()
    
    @staticmethod
    def _get_farm_advisory_prompt() -> ChatPromptTemplate:
        template = """
        **Role:** You are a helpful and reliable AI assistant focused on farm and plantation advisory for African farmers. You retain conversation memory to build relevant and continuous guidance.

        **Objective:** Deliver accurate, easy-to-understand, and actionable advice on farm management—tailored to user-specific details and our prior conversation.

        **Behavior Rules:**
        - NEVER assume or invent crops, locations, or methods not already mentioned.
        - NEVER use the word “context” in replies. Instead, say: "Based on your farm details..."
        - Only reference crops, locations, or methods included in the `farm_context` data or prior conversation.
        - Always build on earlier advice when applicable and maintain consistency.
        - Greet politely if the user sends only a greeting (e.g., “hi”, “hello”, “good morning”).

        **Instructions:**

        1. **Handling Greetings Only:**
        If the message is just a greeting, respond with:

        ```
        🌱 Welcome to CropSense AI! I'm your farm advisory assistant, here to help you with:

        • 🧑‍🌾 *General Farming Advice*: Ask any question about best farming practices.  
        • 🌾 *Crop Recommendations*: Get crop suggestions tailored to your farm.  
        • 🌽 *Crop Image Analysis*: Send a photo for expert feedback.  
        • 🌦️ *Weather Updates*: Get current and forecasted weather for your farm.  

        Just type your question or send a photo to get started!

        Visit our website for full access to services.
        ```

        2. **For All Other Messages:**
        - First, check if this is a new or ongoing conversation.
        - Review conversation history for relevant previous discussions
        - Identify applicable farm-specific details from farm context
        - Connect current question to ongoing farming narrative
        - Note any contradictions or updates to previous information
        - Use general agronomic knowledge only if no farm-specific context is available.
        - Always end with a relevant farming emoji: 🌱🌾🚜🧑‍🌾🌳🌽💰

        3. **Answering Guidelines:**
        - Lead with connection to previous conversation when relevant
        - Integrate specific farm details naturally throughout response
        - Provide actionable, specific advice rather than generic information
        - Include practical implementation steps when possible
        - Consider local African farming constraints (climate, resources, markets)
        - Verify advice is practical for the specific farm context
        - Avoid contradicting earlier guidance without explanation
        - Don't mention crops, locations, or methods not previously discussed or in farm context        

        4. **Weather-Related Responses:**
        If the question is about weather and data is present in `farm_context` data, format the response like:

        ```
        🌤️ Weather in [Location] 🌤️

        📍 Current Conditions:  
        🌡️ Temperature: [temp]°C (feels like [feels_like]°C)  
        ☁️ Conditions: [condition]  
        💧 Humidity: [humidity]%  
        💨 Wind: [wind_speed] km/h

        📅 Today's Forecast:  
        [Time] – [Temp]°C, [Condition]  
        …

        📅 Tomorrow's Forecast:  
        [Time] – [Temp]°C, [Condition]  
        …

        🌾 Farming Advice:  
        [Weather-based recommendation]
        ```

        5. **What to Avoid:**
        - Never use the word "context" in responses - use "Based on your farm details" instead
        - Don't reveal information sources or mention "based on context"
        - Don’t include or repeat the farm name—just refer to “your farm details.”
        - Avoid generic advice when specific farm information is available
        - Don't assume crops, locations, or methods not in conversation or farm context
        - Refuse non-agricultural questions with: "⚠️ I focus exclusively on farming and agricultural questions. How can I help with your farm?"
        - For unanswerable agricultural questions: "⚠️ I don't have enough information to answer that properly. Could you provide more details about your specific situation?"

        ---
        ## Error Handling
        ---
        If you **cannot confidently answer the question** based on the available information (conversation history, farm context, provided context, or general knowledge), or if the question is outside your domain, reply with:
        "⚠️ I apologize, but I'm unable to provide a helpful answer to that question at the moment. Could you please rephrase it or ask something else related to farming? 🌱"

        ---
        ## Tone
        ---
        Maintain a helpful, informative, and professional tone. Be empathetic and encouraging.

        ---
        ## Concluding Remark
        ---
        Consider suggesting relevant next steps or offering further assistance based on our conversation history. For example, "Would you like to know more about [related topic]?"

        **Conversation History:**
        {conversation_context}

        **Farm Context:**
        {farm_context}
    

        **Other Input:**  
        {context}

        **User's Question:** 
        {question}

        **Your Answer:**
        """
        return ChatPromptTemplate.from_template(template)

    @staticmethod
    def _format_docs(docs: List[Document]) -> str:
        """Format retrieved documents into a single string."""
        if not docs:
            return "No relevant context found."
        sources = [doc.metadata.get('source', 'Unknown') for doc in docs]
        logger.debug(f"Context from text retriever sources: {sources[:3]}...")
        return "\n\n".join(doc.page_content for doc in docs)

    def initialize(self) -> bool:
        """
        Initialize the RAG service by loading vector stores and LLM clients.
        
        Returns:
            bool: True if initialization was successful, False otherwise
        """
        try:
            start_time = time.time()
            logger.info("🚀 Initializing RAG Service...")

            self.text_retriever = VectorStoreManager(self.settings).get_text_retriever()

            if not self.text_retriever:
                logger.error("Failed to initialize text retriever")
                return False

            self.is_initialized = True
            elapsed = timedelta(seconds=time.time() - start_time)
            logger.info(f"✅ RAG Service initialized successfully in {elapsed}")
            return True
        except Exception as e:
            logger.exception(f"❌ Failed to initialize RAG Service: {e}")
            self.is_initialized = False
            return False

    def query(self, question: str, llm_provider: str, farm_context: dict, rag: bool, user_id: str, channel_id: str, language: str = "English",conversation_id: Optional[str] = None) -> RagQueryResponse:
        """
        Process a query using the RAG system.
        
        Args:
            question: The user's question
            llm_provider: The LLM provider to use
            farm_context: Context about the farm
            user_id: The user's ID
            channel_id: The channel ID
            language: The language the user is using
            conversation_id: Optional conversation ID. If not provided, will use active conversation or create new one
            
        Raises:
            HTTPException: If the service is not initialized
        """


        start_time = time.time()
        results: RagQueryResponse = {
            "question": question,
            "answer": None,
            "language": language,
            "llm_provider_used": llm_provider,
            "channel_id": channel_id,
            "conversation_id": None,
            "message_id": None
        }

        try:
            if language != "Engish":
                question = self._translate_text(question, "English", llm_provider)
            # Get or create conversation
            conversation = self.conversation_memory.get_active_conversation(user_id, channel_id)
            if not conversation:
                # Generate title from first question
                title = self._generate_conversation_title(question, llm_provider)
                
                # Create new conversation with generated title
                conversation = self.conversation_memory.create_conversation(
                    user_id=user_id,
                    channel_id=channel_id,
                    title=title
                )
                
                if not conversation:
                    raise Exception("Failed to create new conversation")
            
            results["conversation_id"] = conversation.conversation_id
            
            # Add user's question to conversation
            user_message = self.conversation_memory.add_message(
                conversation_id=conversation.conversation_id,
                role="user",
                content=question
            )
            
            if not user_message:
                raise Exception("Failed to save user message")
            
            # Get conversation context
            conversation_context = self.conversation_memory.get_context(
                conversation_id=conversation.conversation_id,
                window_size=int(self.context_window_size)
            )
            
            llm = self._get_llm_client(llm_provider)
            prompt_template = self._get_farm_advisory_prompt()
            
            logger.debug(f"Building dynamic RAG chain with {llm_provider.upper()} LLM and Text Retriever.")

            if rag:
                # Lazily initialize the text retriever if it hasn't been already.
                if not self.is_initialized:
                    if not self.initialize():
                        raise HTTPException(status_code=500, detail="Failed to initialize text retriever for RAG.")

                rag_chain = (
                    RunnableParallel(
                        {
                            "context": self.text_retriever | self._format_docs,
                            "conversation_context": lambda _: conversation_context,
                            "farm_context": lambda _: farm_context,
                            "question": RunnablePassthrough()
                        }
                    )
                    | prompt_template
                    | llm
                    | StrOutputParser()
                )
            else:
                rag_chain = (
                    RunnableParallel(
                        {   'context': lambda _: "",
                            "conversation_context": lambda _: conversation_context,
                            "farm_context": lambda _: farm_context,
                            "question": RunnablePassthrough()
                        }
                    )
                    | prompt_template
                    | llm
                    | StrOutputParser()
                )
                
            logger.debug(f"Invoking dynamic RAG chain for question: '{question[:50]}...'")
            answer = rag_chain.invoke(question)
            
            if language != "English":
                answer = self._translate_text(answer, language, llm_provider)  
            
            
            # Add assistant's response to conversation
            assistant_message = self.conversation_memory.add_message(
                conversation_id=conversation.conversation_id,
                role="assistant",
                content=answer
            )
            
            if not assistant_message:
                raise Exception("Failed to save assistant message")
            
            results["answer"] = answer
            results["message_id"] = assistant_message.message_id
            logger.debug("Dynamic RAG chain finished.")
            query_time = time.time() - start_time
            logger.info(f"Query processed using {llm_provider.upper()} in {timedelta(seconds=query_time)}")
        except Exception as e:
            logger.exception(f"❌ Unexpected error during query processing chain execution with {llm_provider.upper()}: {e}")
            results["answer"] = None
            
        return results

    def clear_conversation(self, conversation_id: str) -> Dict[str, Any]:
        """
        Clear the conversation history for a user in a specific channel.
        
        Args:
            conversation_id: The conversation ID to clear
            
        Returns:
            Dict[str, Any]: Status of the operation
        """
        success = self.conversation_memory.clear_conversation(conversation_id)
        logger.info(f"Cleared conversation history for conversation {conversation_id}")
        return {
            "success": success,
            "conversation_id": conversation_id,
            "message": "Conversation history cleared successfully." if success else "Failed to clear conversation history."
        }

    def get_conversation_history(self, conversation_id: str) -> List[Message]:
        """
        Get the entire message history for a conversation.
        
        Args:
            conversation_id: The conversation ID to get history for
            
        Returns:
            List[Message]: The list of messages in the conversation
        """
        return self.conversation_memory.get_conversation_history(conversation_id)

    def get_conversations_by_user_id(self, user_id: str) -> List[Conversation]:
        """
        Get all conversations for a user.
        
        Args:
            user_id: The user ID to get conversations for
            
        Returns:
            List[Conversation]: The list of conversations for the user
        """
        return self.conversation_memory.get_conversations_by_user_id(user_id)
        
    def _translate_text(self, text: str, language: str, llm_provider: str = "gemini") -> str:
        """
        Translates the RAG response to the specified language.

        Args:
            text: text to translate (string).
            language: The language to translate to (string).
            llm_provider: The LLM provider to use for translation (string, default "gemini").

        Returns:
            str: The translated response, or the original if translation fails.
        """
        try:
            llm = self._get_llm_client(llm_provider)
            prompt = ChatPromptTemplate.from_template(
                """You are a translation expert. Your task is to translate the given text into the specified language. 
                Maintain the original tone and style of the text as much as possible.
                
                Here's the text to translate:
                
                {question}
                
                Translate the above text into: {language}

                Translation:
                """
            )  # Improved prompt template
            chain = prompt | llm | StrOutputParser()
            response = chain.invoke({"question": text, "language": language})
            return response
        except Exception as e:
            logger.error(f"Error generating translated response: {e}")
            return text
                

    def _generate_conversation_title(self, question: str, llm_provider: str = "gemini") -> str:
        """
        Generate a short, descriptive title for a conversation based on the user's first question.
        
        Args:
            question: The user's first question
            llm_provider: The LLM provider to use for title generation
            
        Returns:
            str: A short, descriptive title
        """
        try:
            llm = self._get_llm_client(llm_provider)
            prompt = ChatPromptTemplate.from_template("""
            Generate a short, descriptive title (maximum 5 words) for a conversation that starts with this question:
            "{question}"
            
            The title should:
            1. Be concise and clear
            2. Focus on the main topic
            3. Be relevant to farming/agriculture
            4. Not include question marks or special characters
            5. Be in title case
            
            Title:""")
            
            chain = prompt | llm | StrOutputParser()
            title = chain.invoke({"question": question})
            
            # Clean up the title
            title = title.strip()
            if len(title) > 50:  # Truncate if too long
                title = title[:47] + "..."
                
            return title
            
        except Exception as e:
            logger.error(f"Error generating conversation title: {e}")
            return "New Conversation"  # Fallback title

    def create_conversation(self, user_id: str, channel_id: str, title: Optional[str] = None, first_question: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a new conversation for a user.
        
        Args:
            user_id: The user's ID
            channel_id: The channel ID
            title: Optional title for the conversation
            first_question: Optional first question to generate title from
            
        Returns:
            Dict[str, Any]: The created conversation details
        """
        try:
            # Generate title if not provided and first question is available
            if not title and first_question:
                title = self._generate_conversation_title(first_question)
            
            conversation = self.conversation_memory.create_conversation(user_id, channel_id, title)
            if not conversation:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create new conversation"
                )
                
            return {
                "success": True,
                "conversation_id": conversation.conversation_id,
                "user_id": user_id,
                "channel_id": channel_id,
                "title": title,
                "message": "New conversation created successfully."
            }
        except Exception as e:
            logger.error(f"Error creating conversation: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create conversation: {str(e)}"
            )

    def cleanup(self) -> None:
        """Clean up RAG Service resources, including vector store and database connections."""
        logger.info("Cleaning up RAG Service resources...")
        try:
            VectorStoreManager(self.settings).cleanup()
            logger.info("Vector Store Manager cleanup complete.")

            if self.conversation_memory:
                self.conversation_memory.close_connection()
            logger.info("Conversation Memory Service connection closed.")

            logger.info("RAG Service cleanup completed.")
        except Exception as e:
            logger.error(f"Error during RAG Service cleanup: {e}")

# Global instance
rag_service_instance: Optional["RAGService"] = None

def get_rag_service(request: Request) -> 'RAGService':
    if rag_service_instance is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="RAG Service is not initialized. Check application startup."
        )
    return rag_service_instance
