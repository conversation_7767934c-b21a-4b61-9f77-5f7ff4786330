import requests
from typing import List, Optional
from datetime import datetime, timezone, timedelta

from config import secrets, logger
from schemas.weather import CurrentWeatherData, ForecastWeatherData, WeatherData


class OpenWeatherMapService:
    def __init__(self):
        self.root_api_url = "https://api.openweathermap.org/data/2.5"
        self.api_key = secrets.get("OPENWEATHERMAP_API_KEY")
        
        if not self.api_key:
            logger.error("OpenWeatherMap API key is not configured")
            raise ValueError("OpenWeatherMap API key is not configured")

    def _parse_weather_data(self, data: dict) -> CurrentWeatherData:
        """Parse the API response into CurrentWeatherData object."""
        return CurrentWeatherData(
            temp=data["main"]["temp"],
            feels_like=data["main"]["feels_like"],
            temp_min=data["main"]["temp_min"],
            temp_max=data["main"]["temp_max"],
            pressure=data["main"]["pressure"],
            humidity=data["main"]["humidity"],
            wind_speed=data["wind"]["speed"],
            wind_deg=data["wind"]["deg"],
            cloud_cover=data["clouds"]["all"],
            country_code=data["sys"]["country"],
            condition=data["weather"][0]["description"] if data.get("weather") else None,
            location_name=data["name"],
            lat=data["coord"]["lat"],
            lon=data["coord"]["lon"]
        )

    def get_current_weather_by_name(self, query: str) -> Optional[CurrentWeatherData]:
        try:
            url = f"{self.root_api_url}/weather?q={query}&appid={self.api_key}&units=metric"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            return self._parse_weather_data(data)
        except Exception as e:
            logger.error(f"Unexpected error getting weather data for {query}: {str(e)}")
        
        return None

    def get_weather_forecast_by_name(self, query: str) -> List[ForecastWeatherData]:
        forecasts = []
        
        url = f"{self.root_api_url}/forecast?q={query}&appid={self.api_key}&units=metric"
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        data = response.json()
        now = datetime.now(timezone.utc)
        cutoff_date = now + timedelta(days=5)
        for item in data.get('list', []):
            forecast_date = datetime.fromtimestamp(item['dt'], tz=timezone.utc)
            if forecast_date > cutoff_date:
                continue
            forecasts.append(ForecastWeatherData(
                weather_date=forecast_date,
                weather_description=item['weather'][0]['description'],
                temp=item['main']['temp'],
                temp_min=item['main']['temp_min'],
                temp_max=item['main']['temp_max'],
                feels_like=item['main']['feels_like'],
                humidity=item['main']['humidity']
            ))
        return forecasts

    def get_weather_data_by_location_name(self, location_name: str) -> WeatherData:
        current_weather = self.get_current_weather_by_name(location_name)
        forecast_weather = self.get_weather_forecast_by_name(location_name)

        return WeatherData(
            location_name=location_name,
            current_weather=current_weather,
            forecast_weather=forecast_weather
        )
