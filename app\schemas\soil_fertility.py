from enum import Enum
from typing import List, Optional
from pydantic import BaseModel


class SoilFertilityRating(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    UNKNOWN = "unknown"


class SoilTestData(BaseModel):
    soil_texture: Optional[str] = None
    soil_ph: float
    soil_nitrogen_ppm: float
    soil_phosphorus_ppm: float
    soil_potassium_ppm: float
    soil_moisture_pct: Optional[float] = None
    soil_organic_matter_pct: Optional[float] = None
    soil_temperature: Optional[float] = None

    class Config:
        from_attributes = True


class SoilFertilityResult(BaseModel):
    fertility_score: int
    class_label: str


class SoilFertility(BaseModel):
    rating: SoilFertilityRating
    score: Optional[float] = None
    explanation: Optional[str] = None

    class Config:
        from_attributes = True
