from enum import Enum
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel


class GrowthTimeline(str, Enum):
    Days = "Days"
    Weeks = "Weeks"
    Months = "Months"


class GrowthStage(str, Enum):
    Germination = "Germination"
    Seedling = "Seedling"
    Vegetative = "Vegetative"
    Reproductive = "Reproductive"
    Maturity = "Maturity"
    Harvesting = "Harvesting"


class FieldCropData(BaseModel):
    crop_name: str
    crop_variant: Optional[str] = None
    country_grown: Optional[str] = None
    planting_date: datetime

    class Config:
        from_attributes = True


class GrowthStageData(BaseModel):
    growth_stage: GrowthStage
    start_date: datetime
    end_date: datetime
    timeline_duration_min: int
    timeline_duration_max: int
    growth_stage_treatments: List[str]    

    class Config:
        from_attributes = True    


class CropGrowthTracker(BaseModel):
    crop_name: str
    crop_variant: Optional[str]
    country_grown: Optional[str]
    planting_date: datetime
    timeline: GrowthTimeline    
    total_duration: int
    expected_harvest_date: datetime
    growth_stages: List[GrowthStageData]

    class Config:
        from_attributes = True    
