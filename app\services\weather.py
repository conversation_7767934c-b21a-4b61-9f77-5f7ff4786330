from config import secrets, logger
from schemas.weather import <PERSON>Data, WeatherDataResponse
from utils.weather_google_maps import GoogleWeatherMapService
from utils.weather_openweathermap import OpenWeatherMapService

from utils.llm_gemini import GeminiClient
from langchain_core.prompts import ChatPromptT<PERSON>plate
from fastapi import HTTPException
from datetime import datetime, timezone, timedelta


class WeatherService:
    def __init__(self):
        self.source = "openweathermap"
        self.weather_service = GoogleWeatherMapService() if self.source == "google_maps" else OpenWeatherMapService()
        self.llm = GeminiClient(model=secrets["GEMINI_DEFAULT_MODEL"], temperature=0.0)
    
    def get_weather_data(self, location: str) -> WeatherDataResponse:
        try:
            weather_data = self.weather_service.get_weather_data_by_location_name(location)
            if not weather_data:
                raise HTTPException(
                    status_code=404,
                    detail=f"Weather data not found for location: {location}"
                )
            
            formatted_message = self.format_weather_data(weather_data)
            
            return WeatherDataResponse(
                status="success",
                message=formatted_message,
                data=weather_data
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting weather data: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error processing weather data: {str(e)}"
            )
    
    def format_weather_data(self, weather_data: WeatherData) -> str:
        prompt_template = """
        Format the following weather data into a user-friendly message with emojis and clear sections. 
        Include current conditions, today's forecast, tomorrow's forecast, and farming advice based on the weather conditions.

        Current Weather Data:
        Location: {location_name}
        Temperature: {temp}°C
        Feels Like: {feels_like}°C
        Conditions: {condition}
        Humidity: {humidity}%
        Wind Speed: {wind_speed} km/h

        Forecast Data:
        {forecast_data}

        Instructions:
        1. Start with a header showing location name with weather emoji
        2. Format current conditions with appropriate emojis
        3. Show today's forecast with time and conditions
        4. Show tomorrow's forecast with time and conditions
        5. Add farming advice based on weather conditions
        6. Use emojis to make the output visually appealing
        7. Keep the format consistent with the example below

        Example Format:
        🌤️ Weather in [Location] 🌤️

        📍 Current Conditions:
        🌡️ Temperature: [temp]°C (feels like [feels_like]°C)
        ☁️ Conditions: [condition]
        💧 Humidity: [humidity]%
        💨 Wind: [wind_speed] km/h

        📅 Today's Forecast:
        [Format each forecast entry with time, temperature, and conditions]

        📅 Tomorrow's Forecast:
        [Format each forecast entry with time, temperature, and conditions]

        🌾 Farming Advice:
        [Add relevant farming advice based on weather conditions]

        Now, format the provided weather data following this structure. Return only the formatted text, no JSON or other formatting.
        """
        
        # Format forecast data for the prompt, separating today and tomorrow
        today = datetime.now(timezone.utc).date()
        tomorrow = today + timedelta(days=1)
        
        today_forecasts = []
        tomorrow_forecasts = []
        
        for forecast in weather_data.forecast_weather:
            forecast_date = forecast.weather_date.date()
            forecast_str = f"Time: {forecast.weather_date.strftime('%H:%M')}, " \
                         f"Temp: {forecast.temp}°C, " \
                         f"Condition: {forecast.weather_description}"
            
            if forecast_date == today:
                today_forecasts.append(forecast_str)
            elif forecast_date == tomorrow:
                tomorrow_forecasts.append(forecast_str)
        
        forecast_data = "Today's Forecast:\n" + "\n".join(today_forecasts) + "\n\n" + \
                       "Tomorrow's Forecast:\n" + "\n".join(tomorrow_forecasts)

        # Extract current weather data
        current = weather_data.current_weather

        messages = [
            ("system", "You are a weather data formatter that creates user-friendly, emoji-rich weather reports with farming advice. Return only the formatted text, no JSON or other formatting."),
            ("human", prompt_template)
        ]
        
        prompt = ChatPromptTemplate(messages).invoke(
            {
                "location_name": weather_data.location_name,
                "temp": current.temp,
                "feels_like": current.feels_like,
                "condition": current.condition,
                "humidity": current.humidity,
                "wind_speed": current.wind_speed,
                "forecast_data": forecast_data
            }
        )
        
        return self.llm.prompt_chat_model(prompt, output_format="text")
