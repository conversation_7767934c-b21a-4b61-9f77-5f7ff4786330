from enum import Enum
from typing import List, Optional
from pydantic import BaseModel


class FertilizerType(str, Enum):
    pass


class FertilizerRecommendation(BaseModel):
    soil_texture: Optional[str] = None
    soil_ph: float
    soil_nitrogen_ppm: float
    soil_phosphorus_ppm: float
    soil_potassium_ppm: float
    soil_moisture_pct: Optional[float] = None
    soil_organic_matter_pct: Optional[float] = None
    soil_temperature: Optional[float] = None
    crops: Optional[List[str]] = None


    class Config:
        from_attributes = True


class FertilizerRecommendationResponse(BaseModel):
    fertilizer: str
    quantity: float  # in kg/ha
    application_time: str
    application_method: str
    explanation: str

    class Config:
        from_attributes = True


class FertilizerRecommendationResponseList(BaseModel):
    recommendations: List[FertilizerRecommendationResponse]

    class Config:
        from_attributes = True
