<form>

</form><!DOCTYPE html>
<html>
<head>
    <title>TTS</title>
</head>
<body>

    <textarea id="text-input" rows="5" cols="50"></textarea>
    <br>
    <button id="speak-button">Speak</button>
    <br><br>
    <audio id="audio-player" controls></audio>

    <script>
        document.getElementById('speak-button').addEventListener('click', async () => {
            const text = document.getElementById('text-input').value;

            const response = await fetch('http://127.0.0.1:8000/chat/text-to-speech', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ text: text })
            });

            const blob = await response.blob();
            const url = URL.createObjectURL(blob);
            const audioPlayer = document.getElementById('audio-player');
            audioPlayer.src = url;
            audioPlayer.play();
        });
    </script>

</body>
</html>