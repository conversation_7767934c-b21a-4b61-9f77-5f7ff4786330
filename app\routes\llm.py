import os
import logging
from typing import Annotated
from contextlib import asynccontextmanager
from fastapi import APIRouter, HTTPException, Depends, status, Request, UploadFile, File

from schemas.users import User
import schemas.soil_fertility as sf_schema
import schemas.crop_recommendations as cr_schema
import schemas.fertilizer_recommendations as fr_schema
import schemas.crop_image_analysis as ca_schema
import schemas.soil_test_result as st_schema
import schemas.soil_test_recommendations as str_schema
import schemas.crop_growth_tracker as cgt_schema
import schemas.activity_recommedation as ar_schema

from services.auth import get_current_active_user
from services.api_log import ApiLogsService
from services.soil_fertility import SoilFertilityLLMService
from services.crop_recommendations import CropRecommendationLLMService
from services.fertilizer_recommendations import FertilizerRecommendationLLMService
from services.crop_image_analysis import CropImageAnalysisLLMService
from services.soil_test_recommendations import SoilTestRecommendationLLMService
from services.soil_test_result import SoilTestLLMService
from services.crop_growth_tracker import CropGrowthTrackerLLMService
from services.activity_recommedation import ActivityRecommendationLLMService


# setup logging
logger = logging.getLogger(__name__)  # Add logger
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"

router = APIRouter()
current_user = Depends(get_current_active_user)


@router.post(
    "/soil-fertility",
    response_model=sf_schema.SoilFertility,
    summary="Rate soil fertility based on soil test data",
)
async def get_soil_fertility(
    data: sf_schema.SoilTestData,
    current_user: Annotated[User, current_user],
    request: Request,
):
    """Rate soil fertility based on soil test data"""
    response = SoilFertilityLLMService().get_soil_fertility(data)

    # Log API request and response
    ApiLogsService().log_request(
        user_id=current_user.user_id,
        ip_address=request.client.host,
        path=request.url.path,
        request_body=data.dict(),
        response_body=response.dict(),
    )
    return response


@router.post(
    "/crop-recommendations",
    response_model=cr_schema.CropRecommendationList,
    summary="Recommend crops based on soil test data",
)
async def get_crop_recommendations(
    data: cr_schema.SoilTestData,
    current_user: Annotated[User, current_user],
    request: Request,
):
    """Recommend crops based on soil test data"""
    response = CropRecommendationLLMService().get_crop_recommendations(data, 10)

    # Log API request and response
    ApiLogsService().log_request(
        user_id=current_user.user_id,
        ip_address=request.client.host,
        path=request.url.path,
        request_body=data.dict(),
        response_body=response.dict(),
    )
    return response


@router.post(
    "/fertilizer-recommendations",
    response_model=fr_schema.FertilizerRecommendationResponseList,
    summary="Recommend fertilizers based on soil test data",
)
async def get_fertilizer_recommendation(
    data: fr_schema.FertilizerRecommendation,
    current_user: Annotated[User, current_user],
    request: Request,
):
    """Recommend fertilizers based on soil test data"""
    response = FertilizerRecommendationLLMService().get_fertilizer_recommendations(data)

    # Log API request and response
    ApiLogsService().log_request(
        user_id=current_user.user_id,
        ip_address=request.client.host,
        path=request.url.path,
        request_body=data.dict(),
        response_body=response.dict(),
    )
    return response


@router.post(
    "/crop-image-analysis",
    response_model=ca_schema.CropImageAnalysisList,
    summary="Analyze crop image",
)
async def analyze_crop_image(
    current_user: Annotated[User, current_user],
    request: Request,
    file: UploadFile = File(...),
):
    """Analyze crop image, and return health status and similar crops"""
    response = CropImageAnalysisLLMService().get_crop_analysis(file)

    # Log API request and response
    ApiLogsService().log_request(
        user_id=current_user.user_id,
        ip_address=request.client.host,
        path=request.url.path,
        request_body={"filename": file.filename},
        response_body=response.dict(),
    )
    return response


@router.post(
    "/soil-test-report-analysis",
    response_model=st_schema.SoilTestResponse,
    summary="Analyze soil test report (PDF or image)",
)
async def analyze_soil_test_report(
    current_user: Annotated[User, current_user],
    request: Request,
    file: UploadFile = File(...),
):
    """Extracts soil nutrient data from an uploaded test file."""
    response = SoilTestLLMService().process_soil_test(file)

    # Log API request and response
    # TODO: temporary disable logging
    # ApiLogsService().log_request(
    #     user_id=current_user.user_id,
    #     ip_address=request.client.host,
    #     path=request.url.path,
    #     request_body={"filename": file.filename},
    #     response_body=response.dict(),
    # )
    return response

  
@router.post(
    "/soil-test-recommendations",
    response_model=str_schema.SoilTestRecommendation,
    summary="Get soil test recommendations"
)
async def get_soil_test_recommendations(
    data: str_schema.SoilTestData,
    current_user: Annotated[User, current_user],
    request: Request
):
    """Get soil test recommendations"""
    response = SoilTestRecommendationLLMService().get_soil_test_recommendation(data)

    ApiLogsService().log_request(
        user_id=current_user.user_id,
        ip_address=request.client.host,
        path=request.url.path,
        request_body=data.dict(),
        response_body=response.dict()
    )
    return response


@router.post(
    "/crop-growth-tracker",
    response_model=cgt_schema.CropGrowthTracker,
    summary="Get crop growth tracker"
)
async def get_crop_growth_tracker(
    data: cgt_schema.FieldCropData,
    current_user: Annotated[User, current_user],
    request: Request
):
    """Get crop growth tracker"""
    response = CropGrowthTrackerLLMService().get_crop_growth_tracker(data)

    '''
    # TODO: fix issue with logging api request
    ApiLogsService().log_request(
        user_id=current_user.user_id,
        ip_address=request.client.host,
        path=request.url.path,
        request_body=data.dict(),
        response_body=response.dict()
    )
    '''
    return response

@router.post(
    "/activity-recommendation",
    response_model=ar_schema.ActivityRecommendationResponse,
    summary="Get activity recommendation"
)
async def get_activity_recommendation(
    data: ar_schema.ActivityRecommendationRequest,
    current_user: Annotated[User, current_user],
    request: Request
):
    """Get activity recommendation"""
    response = ActivityRecommendationLLMService().get_activity_recommendation(data)

    # ApiLogsService().log_request(
    #     user_id=current_user.user_id,
    #     ip_address=request.client.host,
    #     path=request.url.path,
    #     request_body=data.dict(),
    #     response_body=response.dict()
    # )
    return response     

################## Soil Fertility ######################
# Sample Request:
# {
#     "soil_type": "loam",
#     "soil_pH": 6.5,
#     "nitrogen": 0.2,
#     "phosphorus": 0.1,
#     "potassium": 0.1,
#     "organic_matter": 2.0,
#     "temperature": 25.0,
#     "rainfall": 800.0,
#     "humidity": 80.0
# }

# Sample Response:
# {
#     "rating": "medium",
#     "score": 50.0,
#     "explanation": "The soil is moderately fertile with a balanced pH and nutrient profile. It is suitable for a wide range of crops."
# }

################## Crop Recommendations ######################
# Sample request body:
# {
#   "soil_texture": "loamy",
#   "soil_ph": 6.5,
#   "soil_nitrogen_ppm": 0.2,
#   "soil_phosphorus_ppm": 0.1,
#   "soil_potassium_ppm": 0.1,
#   "soil_moisture_pct": 65,
#   "soil_organic_matter_pct": 2.5,
#   "soil_temperature": 30,
#   "country": "Nigeria",
#   "specified_preferences": ["I prefer drought-resistant crops", "I want high-cash crops"]
# }

################## Fertilizer Recommendations ######################
# Sample Request:
# {
#     "soil_type": "loam",
#     "soil_pH": 6.5,
#     "nitrogen": 0.2,
#     "phosphorus": 0.1,
#     "potassium": 0.1,
#     "organic_matter": 2.0,
#     "temperature": 25.0,
#     "rainfall": 800.0,
#     "humidity": 80.0,
#     "crop": "Maize",
#     "field_size_hct": 2.0
# }
