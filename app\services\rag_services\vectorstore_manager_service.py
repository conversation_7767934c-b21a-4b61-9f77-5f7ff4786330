import os
import gc
import tempfile
import boto3
from pathlib import Path
from typing import Optional, List, Dict, Any, Tuple, Union
from dataclasses import dataclass

from langchain_core.documents import Document
from langchain_core.vectorstores import VectorStoreRetriever
from langchain_community.vectorstores.utils import filter_complex_metadata
from langchain_community.vectorstores import FAISS
from langchain_openai import OpenAIEmbeddings
from langchain_huggingface import HuggingFaceEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import (
    DirectoryLoader,
    TextLoader
)

# Local imports
from config import secrets, logger
from services.rag_services.rag_config import Settings

# Set Env
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"

# Global variables
FAISS_INDEX_NAME = "farm_practices"


@dataclass
class S3Keys:
    """Container for S3 key information."""
    bucket: str
    index_key: str
    docstore_key: str


@dataclass
class StoreInfo:
    """Container for vector store information."""
    collection_path: Path
    index_path: Path
    docstore_path: Path
    cache_key: str


class EmbeddingFactory:
    """Factory for creating embedding models."""
    
    @staticmethod
    def create_embeddings(config: Settings) -> Union[OpenAIEmbeddings, HuggingFaceEmbeddings]:
        """Create embeddings model with fallback strategy."""
        openai_embeddings = EmbeddingFactory._try_openai_embeddings()
        if openai_embeddings:
            return openai_embeddings
            
        return EmbeddingFactory._create_huggingface_embeddings(config)
    
    @staticmethod
    def _try_openai_embeddings() -> Optional[OpenAIEmbeddings]:
        """Attempt to create OpenAI embeddings if API key is available."""
        api_key = secrets.get("OPENAI_API_KEY")
        if not api_key:
            return None
            
        try:
            logger.info("Initializing OpenAI embeddings.")
            return OpenAIEmbeddings(openai_api_key=api_key)
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI embeddings despite key presence: {e}")
            return None
    
    @staticmethod
    def _create_huggingface_embeddings(config: Settings) -> HuggingFaceEmbeddings:
        """Create HuggingFace embeddings as fallback."""
        try:
            logger.warning(f"OpenAI not available. Falling back to HuggingFace model: {config.EMBEDDING_MODEL}")
            return HuggingFaceEmbeddings(
                model_name=config.EMBEDDING_MODEL,
                model_kwargs={"device": config.DEVICE},
                encode_kwargs={'normalize_embeddings': True}
            )
        except Exception as e:
            logger.exception("FATAL: Could not initialize any embedding model.")
            raise e


class S3Manager:
    """Handles S3 operations for vector stores."""
    
    def __init__(self, config: Settings):
        self.config = config
        self.client = self._initialize_client() if config.USE_S3 else None
    
    def _initialize_client(self) -> Optional[boto3.client]:
        """Initialize S3 client with proper error handling."""
        s3_conf = self.config.S3_CONFIG
        
        if not self._has_required_credentials(s3_conf):
            logger.warning("S3 credentials not fully configured. S3 operations will be disabled.")
            return None
        
        try:
            return boto3.client(
                's3',
                endpoint_url=s3_conf.endpoint_url,
                aws_access_key_id=s3_conf.aws_access_key_id,
                aws_secret_access_key=s3_conf.aws_secret_access_key,
                region_name=s3_conf.region_name
            )
        except Exception as e:
            logger.exception(f"Failed to initialize S3 client: {e}")
            return None
    
    @staticmethod
    def _has_required_credentials(s3_conf) -> bool:
        """Check if all required S3 credentials are present."""
        return all([
            s3_conf.aws_access_key_id,
            s3_conf.aws_secret_access_key,
            s3_conf.region_name
        ])
    
    def get_s3_keys(self, collection: str, index_name: str) -> S3Keys:
        """Generate S3 keys for a collection."""
        bucket = self.config.S3_CONFIG.aws_bucket
        prefix = self.config.S3_CONFIG.vectorstore_prefix
        
        return S3Keys(
            bucket=bucket,
            index_key=f"{prefix}/{index_name}.faiss",
            docstore_key=f"{prefix}/{index_name}.pkl"
        )
    
    def upload_store(self, store_info: StoreInfo, collection: str, index_name: str) -> bool:
        """Upload vector store files to S3."""
        if not self.client:
            return False
            
        if not self._local_files_exist(store_info):
            logger.error(f"Local FAISS files not found in {store_info.collection_path} for collection '{collection}'.")
            return False
        
        s3_keys = self.get_s3_keys(collection, index_name)
        return self._upload_files(store_info, s3_keys)
    
    def download_store(self, store_info: StoreInfo, collection: str, index_name: str) -> bool:
        """Download vector store files from S3."""
        if not self.client:
            return False
            
        s3_keys = self.get_s3_keys(collection, index_name)
        
        if not self._files_exist_in_s3(s3_keys):
            logger.warning(f"FAISS files for '{collection}' not found in S3 bucket '{s3_keys.bucket}'.")
            return False
        
        return self._download_files(store_info, s3_keys)
    
    def _local_files_exist(self, store_info: StoreInfo) -> bool:
        """Check if local FAISS files exist."""
        return store_info.index_path.exists() and store_info.docstore_path.exists()
    
    def _files_exist_in_s3(self, s3_keys: S3Keys) -> bool:
        """Check if files exist in S3."""
        try:
            self.client.head_object(Bucket=s3_keys.bucket, Key=s3_keys.index_key)
            self.client.head_object(Bucket=s3_keys.bucket, Key=s3_keys.docstore_key)
            return True
        except Exception:
            return False
    
    def _upload_files(self, store_info: StoreInfo, s3_keys: S3Keys) -> bool:
        """Upload index and docstore files to S3."""
        try:
            logger.info(f"Uploading FAISS index to S3: s3://{s3_keys.bucket}/{s3_keys.index_key}")
            self.client.upload_file(str(store_info.index_path), s3_keys.bucket, s3_keys.index_key)

            logger.info(f"Uploading FAISS docstore to S3: s3://{s3_keys.bucket}/{s3_keys.docstore_key}")
            self.client.upload_file(str(store_info.docstore_path), s3_keys.bucket, s3_keys.docstore_key)
            return True
        except Exception as e:
            logger.exception(f"Error saving FAISS files to S3: {e}")
            return False
    
    def _download_files(self, store_info: StoreInfo, s3_keys: S3Keys) -> bool:
        """Download index and docstore files from S3."""
        try:
            store_info.collection_path.mkdir(parents=True, exist_ok=True)
            
            logger.info(f"Downloading FAISS index from S3 ({s3_keys.index_key}) to {store_info.index_path}")
            self.client.download_file(s3_keys.bucket, s3_keys.index_key, str(store_info.index_path))

            logger.info(f"Downloading FAISS docstore from S3 ({s3_keys.docstore_key}) to {store_info.docstore_path}")
            self.client.download_file(s3_keys.bucket, s3_keys.docstore_key, str(store_info.docstore_path))
            return True
        except Exception as e:
            logger.exception(f"Error loading FAISS files from S3: {e}")
            return False


class VectorStoreFactory:
    """Factory for creating and managing vector stores."""
    
    def __init__(self, embedding_model, config: Settings):
        self.embedding_model = embedding_model
        self.config = config
    
    def create_store(self, documents: List[Document], store_info: StoreInfo) -> Optional[FAISS]:
        """Create a new FAISS vector store from documents."""
        if not self.embedding_model or not documents:
            return None

        store_info.collection_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"🛠️ Creating FAISS store in '{store_info.collection_path}' from {len(documents)} documents.")

        try:
            filtered_docs = self._filter_documents(documents)
            if not filtered_docs:
                return None

            vector_store = FAISS.from_documents(documents=filtered_docs, embedding=self.embedding_model)
            self._save_store_locally(vector_store, store_info)
            
            return vector_store

        except Exception as e:
            logger.exception(f"❌ Error creating FAISS store: {e}")
            return None
        finally:
            gc.collect()
    
    def load_store(self, store_info: StoreInfo) -> Optional[FAISS]:
        """Load an existing FAISS vector store."""
        if not store_info.index_path.exists():
            logger.warning(f"No local FAISS index found at '{store_info.index_path}'.")
            return None

        logger.info(f"🔍 Loading FAISS store from local directory '{store_info.collection_path}'.")
        try:
            return FAISS.load_local(
                folder_path=str(store_info.collection_path),
                index_name=FAISS_INDEX_NAME,
                embeddings=self.embedding_model,
                allow_dangerous_deserialization=True
            )
        except Exception as e:
            logger.exception(f"❌ Error loading FAISS store from local directory '{store_info.collection_path}': {e}")
            return None
    
    def _filter_documents(self, documents: List[Document]) -> Optional[List[Document]]:
        """Filter out documents with complex metadata."""
        filtered_docs = filter_complex_metadata(documents)
        if not filtered_docs:
            logger.warning("All documents filtered out; aborting store creation.")
            return None
        return filtered_docs
    
    def _save_store_locally(self, store: FAISS, store_info: StoreInfo) -> None:
        """Save vector store to local filesystem."""
        store.save_local(
            folder_path=str(store_info.collection_path), 
            index_name=FAISS_INDEX_NAME
        )
        logger.info(f"Saved FAISS store locally to '{store_info.collection_path}'.")


class StoreInfoFactory:
    """Factory for creating StoreInfo objects."""
    
    @staticmethod
    def create_store_info(persist_dir: str, collection: str, index_name: str) -> StoreInfo:
        """Create StoreInfo for a given collection."""
        collection_path = Path(persist_dir) / collection
        return StoreInfo(
            collection_path=collection_path,
            index_path=collection_path / f"{index_name}.faiss",
            docstore_path=collection_path / f"{index_name}.pkl",
            cache_key=f"{persist_dir}:{collection}"
        )


class DocumentProcessor:
    """Handles document loading and splitting."""
    def __init__(self, config: Settings):
        self.config = config
        self.CHUNK_SIZE = 1000
        self.CHUNK_OVERLAP = 200
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.CHUNK_SIZE,
            chunk_overlap=self.CHUNK_OVERLAP
        )
        self.s3 = None
        if config.USE_S3:
            s3_conf = config.S3_CONFIG
            # Basic check - actual connection happens on use
            if all([s3_conf.aws_access_key_id, s3_conf.aws_secret_access_key, s3_conf.region_name]):
                try:
                    self.s3 = boto3.client(
                        's3',
                        endpoint_url=s3_conf.endpoint_url,
                        aws_access_key_id=s3_conf.aws_access_key_id,
                        aws_secret_access_key=s3_conf.aws_secret_access_key,
                        region_name=s3_conf.region_name
                    )
                    logger.info(f"S3 client configured for region {s3_conf.region_name}")
                except Exception as e:
                    logger.exception(f"Failed to configure S3 client: {e}")
                    self.s3 = None
            else:
                logger.warning("S3 enabled but credentials/region missing. S3 loading will fail.")

    def load_documents(self, doc_type: str = "text") -> List[Document]:
        """Loads documents based on type ('text' or 'pdf') from configured source."""
        if doc_type == "text":
            if self.config.USE_S3: 
                return self._load_s3_docs(
                    self.config.S3_CONFIG.aws_bucket, 
                    f"{self.config.S3_CONFIG.rag_documents_prefix}/texts/", ".txt", "Text"
                )
            else: 
                return self._load_local_docs('./data/texts', '*.txt', TextLoader, "Text")
        else:
            logger.error(f"Unsupported document type: {doc_type}")
            return []

    def _load_local_docs(self, dir_path: str, glob_pattern: str, loader_cls, doc_type_name: str) -> List[Document]:
        """Helper to load local documents."""
        logger.info(f"📂 Loading local {doc_type_name} documents from {dir_path} (pattern: {glob_pattern})")
        local_dir = Path(dir_path)
        if not local_dir.is_dir():
            logger.error(f"Local {doc_type_name} directory not found: {local_dir}")
            return []
        try:
            loader_kwargs = {}

            loader = DirectoryLoader(
                str(local_dir),
                glob=glob_pattern,
                loader_cls=loader_cls,
                loader_kwargs=loader_kwargs if loader_kwargs else None,
                show_progress=True,
                use_multithreading=True, 
            )
            documents = loader.load()
            # Add standardized metadata
            for doc in documents:
                source = doc.metadata.get("source", "Unknown")
                page = doc.metadata.get("page_number") # Specific to Unstructured loaders
                doc.metadata["file_name"] = Path(source).name if source != "Unknown" else "Unknown"
                doc.metadata["file_type"] = doc_type_name.lower()
                if page is not None: doc.metadata["page"] = page

            logger.info(f"✅ Loaded {len(documents)} local {doc_type_name} documents from {dir_path}")
            return documents
        except Exception as e:
            logger.exception(f"❌ Error loading local {doc_type_name} documents from {dir_path}: {e}")
            return []

    def _load_s3_docs(self, bucket: Optional[str], prefix: Optional[str], suffix: str, doc_type_name: str) -> List[Document]:
        """Helper to load documents from S3."""
        if not self.s3:
            logger.error(f"S3 client not initialized. Cannot load {doc_type_name} documents from S3.")
            return []
        if not bucket:
             logger.error(f"S3 {doc_type_name} bucket name is not configured.")
             return []
        prefix = prefix or ""
        logger.info(f"☁️ Loading {doc_type_name} files from s3://{bucket}/{prefix} (suffix: {suffix})")

        s3_keys = []
        try:
            paginator = self.s3.get_paginator('list_objects_v2')
            pages = paginator.paginate(Bucket=bucket, Prefix=prefix)
            for page in pages:
                 if 'Contents' in page:
                     s3_keys.extend([
                         obj['Key'] for obj in page['Contents']
                         if obj['Key'].lower().endswith(suffix) and obj.get('Size', 0) > 0 and not obj['Key'].endswith('/')
                     ])
        except Exception as e:
             logger.exception(f"❌ Error listing S3 objects in s3://{bucket}/{prefix}: {e}")
             return []

        if not s3_keys:
            logger.warning(f"⚠️ No matching {doc_type_name} files found in s3://{bucket}/{prefix}")
            return []

        logger.info(f"Found {len(s3_keys)} {doc_type_name} files in S3.")
        all_documents = []
        processed_files = 0
        for key in s3_keys:
            logger.debug(f"Processing S3 {doc_type_name}: s3://{bucket}/{key}")
            # temp_path = None # No longer needed outside the 'with' block

            try:
                # Use delete=True (default) or omit it.
                # Process the file *inside* the 'with' block.
                with tempfile.NamedTemporaryFile(suffix=suffix, mode='wb+') as temp_file: # Use binary mode for download_file
                    self.s3.download_file(bucket, key, temp_file.name)
                    temp_path_inside_with = temp_file.name # Path is valid inside 'with'

                    # Ensure buffer is flushed before loader reads it
                    temp_file.flush()
                    os.fsync(temp_file.fileno()) # Ensure changes are written to disk

                    # Choose loader based on suffix
                    if suffix == ".txt":
                        # TextLoader needs the path, which is temp_file.name
                        loader = TextLoader(temp_path_inside_with, encoding='utf-8') # Specify encoding
                        docs = loader.load()
                    else:
                        logger.warning(f"Unsupported suffix {suffix} for S3 loading, skipping {key}")
                        docs = []

                    # Add standardized metadata
                    s3_source_uri = f"s3://{bucket}/{key}"
                    file_name = os.path.basename(key)
                    for i, doc in enumerate(docs):
                        doc.metadata["source"] = s3_source_uri
                        doc.metadata["file_name"] = file_name
                        doc.metadata["file_type"] = doc_type_name.lower()
                        page = doc.metadata.get("page_number") # Specific to Unstructured
                        if page is not None: doc.metadata["page"] = page
                        elif len(docs) > 1: doc.metadata["page"] = i + 1 # Fallback

                    all_documents.extend(docs)
                    processed_files += 1
                    logger.debug(f"📄 Processed S3 {doc_type_name} {key}: loaded {len(docs)} document parts.")

                # temp_file is now closed and automatically deleted here (if delete=True)

            except Exception as e:
                logger.exception(f"⚠️ Error processing S3 {doc_type_name} file s3://{bucket}/{key}: {e}")
                # No need to manually delete temp_file here as the 'with' handles it on error too
                continue # Skip file

        logger.info(f"✅ Loaded {len(all_documents)} document parts from {processed_files} S3 {doc_type_name} files.")
        return all_documents
    
    def split_documents(self, documents: List[Document]) -> List[Document]:
        """Splits documents into chunks using the configured splitter."""
        if not documents:
            logger.warning("No documents provided for splitting.")
            return []
        logger.info(f"✂️ Splitting {len(documents)} documents into chunks (size={self.CHUNK_SIZE}, overlap={self.CHUNK_OVERLAP})")
        try:
            chunks = self.text_splitter.split_documents(documents)
            logger.info(f"✅ Generated {len(chunks)} chunks.")
            # Metadata is usually preserved by the splitter
            return chunks
        except Exception as e:
            logger.exception(f"❌ Error splitting documents: {e}")
            return []


class VectorStoreManager:
    """
    Manages FAISS vector store creation, loading, and retrieval with S3 support.
    Each 'collection' is stored in its own sub-folder, containing fixed-name index files.
    """
    
    def __init__(self, config: Settings):
        self.config = config
        self.embedding_model = EmbeddingFactory.create_embeddings(config)
        self._stores: Dict[str, FAISS] = {}
        
        # Initialize managers
        self.s3_manager = S3Manager(config)
        self.store_factory = VectorStoreFactory(self.embedding_model, config)
    
    def get_text_retriever(self, top_k: Optional[int] = None) -> Optional[VectorStoreRetriever]:
        """Get a retriever for text documents."""
        store = self._get_or_create_store(
            store_dir='/tmp',
            collection=self.config.S3_CONFIG.vectorstore_prefix.split('/')[-1],
            doc_type="text"
        )
        return self._create_retriever(store, top_k)
    
    def get_pdf_retriever(self, top_k: Optional[int] = None) -> Optional[VectorStoreRetriever]:
        """Get a retriever for PDF documents."""
        store = self._get_or_create_store(
            store_dir='/tmp',
            collection="farm_practices_pdf",
            doc_type="text"
        )
        return self._create_retriever(store, top_k)
    
    def cleanup(self):
        """Clear the in-memory cache of vector stores."""
        logger.info("Cleaning up vector store manager: clearing in-memory cache.")
        self._stores.clear()
        gc.collect()
    
    def _get_or_create_store(self, store_dir: str, collection: str, doc_type: str) -> Optional[FAISS]:
        """Get existing store or create new one from documents."""
        store_info = StoreInfoFactory.create_store_info(store_dir, collection, FAISS_INDEX_NAME)
        
        # Try to get from cache first
        if store_info.cache_key in self._stores:
            return self._stores[store_info.cache_key]
        
        # Try to load existing store
        store = self._load_existing_store(store_info, collection)
        if store:
            self._stores[store_info.cache_key] = store
            return store
        
        # Create new store from documents
        return self._create_new_store(store_info, collection, doc_type)
    
    def _load_existing_store(self, store_info: StoreInfo, collection: str) -> Optional[FAISS]:
        """Load existing vector store from local storage or S3."""
        backend = self.config.VECTOR_STORE_BACKEND
        
        # Try to download from S3 if configured
        if backend in ("hybrid", "s3"):
            s3_success = self.s3_manager.download_store(store_info, collection, FAISS_INDEX_NAME)
            if not s3_success and backend == "s3":
                return None
        
        # Load from local storage
        return self.store_factory.load_store(store_info)
    
    def _create_new_store(self, store_info: StoreInfo, collection: str, doc_type: str) -> Optional[FAISS]:
        """Create a new vector store from documents."""
        logger.warning(f"Store '{collection}' not found. Creating from '{doc_type}' documents...")
        
        documents = self._load_documents(doc_type)
        if not documents:
            logger.error(f"No '{doc_type}' documents found to create store '{collection}'.")
            return None
        
        store = self.store_factory.create_store(documents, store_info)
        if not store:
            return None
        
        # Handle S3 operations based on backend configuration
        self._handle_s3_operations(store_info, collection)
        
        # Cache the store
        self._stores[store_info.cache_key] = store
        return store
    
    def _load_documents(self, doc_type: str) -> List[Document]:
        """Load documents using the document processor."""
        doc_processor = DocumentProcessor(self.config)
        return doc_processor.load_documents(doc_type)
    
    def _handle_s3_operations(self, store_info: StoreInfo, collection: str) -> None:
        """Handle S3 upload and local cleanup based on backend configuration."""
        backend = self.config.VECTOR_STORE_BACKEND
        
        if backend in ("hybrid", "s3"):
            upload_success = self.s3_manager.upload_store(store_info, collection, FAISS_INDEX_NAME)
            
            # Remove local files if using S3-only backend
            if backend == "s3" and upload_success:
                self._cleanup_local_files(store_info)
    
    def _cleanup_local_files(self, store_info: StoreInfo) -> None:
        """Remove local FAISS files after S3 upload."""
        store_info.index_path.unlink(missing_ok=True)
        store_info.docstore_path.unlink(missing_ok=True)
    
    def _create_retriever(self, store: Optional[FAISS], top_k: Optional[int]) -> Optional[VectorStoreRetriever]:
        """Create a retriever from a vector store."""
        if not store:
            return None
        
        search_k = top_k if top_k is not None else self.config.TOP_K
        return store.as_retriever(search_kwargs={"k": search_k})
    