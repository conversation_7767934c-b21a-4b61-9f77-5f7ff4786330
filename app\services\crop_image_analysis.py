import langchain
import json
import uuid
import base64
import boto3
import pandas as pd
from rapidfuzz import process
from datetime import datetime
from io import BytesIO
from PIL import Image
from mimetypes import guess_type
from fastapi import HTTPException, status
from config import secrets, logger
from utils.llm_gemini import Gemini<PERSON><PERSON>

from typing import List, Dict
import schemas.crop_image_analysis as schema
from schemas.crop_image_analysis import CropImageAnalysis

# Configurations for langchain
langchain.verbose = False
langchain.debug = False
langchain.llm_cache = False


class CropImageAnalysisLLMService:
    """
    Service to analyze agricultural crop images using the Gemini model.
    """
    llm = GeminiClient(model=secrets["GEMINI_DEFAULT_MODEL"], temperature=0.0)
    s3_client = boto3.client(
        "s3",
        aws_access_key_id=secrets["AWS_ACCESS_KEY_ID"],
        aws_secret_access_key=secrets["AWS_SECRET_ACCESS_KEY"]
    )
    bucket_name = secrets['AWS_S3_BUCKET']
    MAX_IMAGE_WIDTH = 800  # Maximum width for resized images

    def __init__(self, similarity_csv_path: str = "data/cleaned_max_crop_pair_similarity.csv"):
        self.similarity_df = pd.read_csv(similarity_csv_path)  # TODO: save this in S3
        self.similarity_df['Crop 1'] = self.similarity_df['Crop 1'].str.strip().str.lower()
        self.similarity_df['Crop 2'] = self.similarity_df['Crop 2'].str.strip().str.lower()

    @staticmethod
    def match_crop_name(query_crop: str, known_crops: List[str], threshold: float = 80) -> str:
        match, score, _ = process.extractOne(query_crop, known_crops)
        return match if score >= threshold else None

    def _resize_image(self, image_file) -> BytesIO:
        try:
            img = Image.open(image_file)
            if img.width > self.MAX_IMAGE_WIDTH:
                ratio = self.MAX_IMAGE_WIDTH / float(img.width)
                new_height = int(float(img.height) * float(ratio))
                img = img.resize((self.MAX_IMAGE_WIDTH, new_height), Image.LANCZOS)

            output = BytesIO()
            img_format = img.format or 'JPEG'
            img.save(output, format=img_format, quality=85)
            output.seek(0)
            return output
        except Exception as e:
            logger.error(f"Error resizing image: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Error processing image"
            ) from e

    def upload_to_s3(self, file) -> str:
        try:
            file_extension = file.filename.rsplit(".", 1)[-1]
            unique_filename = f"{datetime.now().strftime('%Y-%m-%d')}/{uuid.uuid4()}.{file_extension}"
            s3_key = f"{secrets['DB_SCHEMA']}/api-requests/uploads/llm/crop-image-analysis/{unique_filename}"
            resized_image = self._resize_image(file.file)
            self.s3_client.upload_fileobj(resized_image, self.bucket_name, s3_key)
            return s3_key
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error uploading file to S3: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error uploading file to S3"
            ) from e

    def read_image_from_s3(self, s3_key: str) -> str:
        try:
            s3_object = self.s3_client.get_object(Bucket=self.bucket_name, Key=s3_key)
            image_data = s3_object["Body"].read()
            mime_type, _ = guess_type(s3_key)
            mime_type = mime_type or "image/jpeg"
            encoded_image = base64.b64encode(image_data).decode("utf-8")
            return f"data:{mime_type};base64,{encoded_image}"
        except Exception as e:
            logger.error(f"Error reading image from S3: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error reading image from S3"
            ) from e

    def analyze_crop_image_prompt(self, image_path: str) -> dict:
        prompt_text = """
        You are an expert in agricultural analysis. Analyze the attached image to determine the crop type(s) present and evaluate the health of each crop based solely on visual cues. Please follow these instructions:

        1. Identify the crop(s) present in the image.
        2. Assess the health of each crop. Crop health status can only be healthy, stressed, diseased or uncertain.        
        3. Provide the output in JSON format with the following structure:
        {
          "crops": [
            {
              "name": "crop_name",
              "health_status": "health_status",
              "observations": "additional_observations"
            }
          ]
        }
        4. If you are uncertain about any detail, please indicate it in the "health" field (for example, "uncertain" or "insufficient visual evidence").
        """
        try:
            return self.llm.prompt_with_image(
                prompt_text=prompt_text,
                image_paths=[image_path],
                output_format="json"
            )
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to parse model response as JSON"
            ) from e
        except Exception as e:
            logger.error(f"Error analyzing crop image: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error analyzing crop image"
            ) from e

    def get_top_similar_crops(self, crop_name: str, top_n: int = 5) -> List[Dict]:
        crop_name = crop_name.strip().lower()
        logger.info(f"Searching for similar crops to: {crop_name}")
        results = []

        for _, row in self.similarity_df.iterrows():
            crop1 = row['Crop 1']
            crop2 = row['Crop 2']
            score = row['Max Similarity']
            if crop1 == crop_name:
                results.append({'target_crop': crop2, 'similarity_score': score})
            elif crop2 == crop_name:
                results.append({'target_crop': crop1, 'similarity_score': score})

        if not results:
            logger.warning(f"No similarity entries found for crop: {crop_name}")

        return sorted(results, key=lambda x: x['similarity_score'], reverse=True)[:top_n]

    def get_crop_analysis(self, image_file) -> schema.CropImageAnalysisList:
        try:
            s3_key = self.upload_to_s3(image_file)
            data_url = self.read_image_from_s3(s3_key)
            result = self.analyze_crop_image_prompt(data_url)

            known_crop_names = list(set(
                self.similarity_df["Crop 1"].tolist() + self.similarity_df["Crop 2"].tolist()
            ))

            for crop in result["crops"]:
                original_crop_name = crop['name']
                normalized_crop_name = self.match_crop_name(original_crop_name.lower(), known_crop_names)

                if normalized_crop_name:
                    logger.info(f"Matched crop '{original_crop_name}' to '{normalized_crop_name}'")
                    crop['similar_crops'] = self.get_top_similar_crops(normalized_crop_name)
                else:
                    logger.warning(f"No fuzzy match found for: {original_crop_name}")
                    crop['similar_crops'] = []

            return schema.CropImageAnalysisList(
                crops=result["crops"],
                total=len(result["crops"])
            )
        except Exception as e:
            logger.error(f"Error in get_crop_analysis: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error analyzing crop image"
            ) from e
