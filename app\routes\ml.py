from typing import Annotated
from fastapi import APIRouter, HTTPException, Depends, status, Request,UploadFile,File

from schemas.users import User
import schemas.soil_fertility as schema

from services.auth import get_current_active_user
from services.api_log import ApiLogsService
from services.soil_fertility import SoilFertilityMLService

router = APIRouter()
current_user = Depends(get_current_active_user)

@router.post(
    "/soil-fertility",
    response_model=schema.SoilFertility,
    summary="Rate soil fertility based on soil test data"
)
async def get_soil_fertility(
    data: schema.SoilTestData,
    current_user: Annotated[User, current_user],
    request: Request
):
    """Rate soil fertility based on soil test data"""
    response = SoilFertilityMLService().get_soil_fertility_prediction(data)

    # Log API request and response
    ApiLogsService().log_request(
        user_id=current_user.user_id,
        ip_address=request.client.host,
        path=request.url.path,
        request_body=data.dict(),
        response_body=response.dict()
    )
    return response

