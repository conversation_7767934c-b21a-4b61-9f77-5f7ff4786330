import json
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, status
from typing import <PERSON><PERSON>, Optional
from langchain_core.prompts import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>emplate

from config import logger, secrets
from utils.llm_gemini import GeminiClient
import schemas.intent_classification as schema


class IntentClassifierService:
    """
    A class to classify user queries into predefined intents using a Large Language Model (Gemini).
    """
    def __init__(self):
        self.llm = GeminiClient(
            model=secrets['GEMINI_DEFAULT_MODEL'], 
            temperature=0.0
        )
        self.valid_intents = ["Weather", "Diagnoses", "Farm Actions", "General", "Ambiguous"]

    def create_system_prompt_template(self, query: str) -> str:
        prompt_text = """
        Classify the user query into one of these intents: {intents_str}.
        Query: {query}
        Output a JSON object: {{"user_query": <query>, "intent": <intent>, "location": <location or null>}}.
        - intent: Must be exactly one of the above.
        - location: Only extract if intent is Weather and a specific location is mentioned; else null.
        Examples:
        User: What's the weather like in Abuja tomorrow?
        Output: {{"user_query": "What's the weather like in Abuja tomorrow?", "intent": "Weather", "location": "Abuja"}}
        User: Is it sunny right now?
        Output: {{"user_query": "Is it sunny right now?", "intent": "Weather", "location": null}}
        User: My maize leaves are turning yellow. What's wrong?
        Output: {{"user_query": "My maize leaves are turning yellow. What's wrong?", "intent": "Diagnoses", "location": null}}
        User: Log my planting activity for rice today.
        Output: {{"user_query": "Log my planting activity for rice today.", "intent": "Farm Actions", "location": null}}
        User: What is crop rotation?
        Output: {{"user_query": "What is crop rotation?", "intent": "General", "location": null}}
        User: Tell me a joke.
        Output: {{"user_query": "Tell me a joke.", "intent": "Ambiguous", "location": null}}
        """
        messages = [
            ("system", "You are an AI Assistant. Classify the query and extract location if needed."),
            ("human", prompt_text)            
        ]
        prompt = ChatPromptTemplate(messages).invoke(
            {"intents_str": json.dumps(self.valid_intents), "query": query}
        )
        return self.llm.prompt_chat_model(prompt)

    def classify_query(self, query: str) -> schema.IntentClassificationOutput:
        try:
            response = self.create_system_prompt_template(query)

            return schema.IntentClassificationOutput(
                query=query,
                intent=response['intent'],
                location=response['location']
            )
        except Exception as e:
            logger.error(f"Error classifying query: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error classifying query"
            )        
