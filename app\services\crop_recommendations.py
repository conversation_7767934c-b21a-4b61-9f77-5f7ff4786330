import langchain
import pandas as pd
from fuzzywuzzy import fuzz
from fastapi import HTTPException, status
from langchain_core.prompts import ChatPromptTemplate

from config import secrets, logger
import schemas.crop_recommendations as schema
from utils.llm_openai import OpenAIClient

# config
langchain.verbose = False
langchain.debug = False
langchain.llm_cache = False


def range_fit(value, min_val, max_val):
    try:
        if pd.isna(min_val) or pd.isna(max_val):
            return 0
        center = (min_val + max_val) / 2
        spread = (max_val - min_val) / 2 or 1e-5
        return max(0, 1 - abs(value - center) / spread)
    except:
        return 0


def safe_float(val):
    try:
        return float(val)
    except (ValueError, TypeError):
        return 0.0


class CropRecommendationMLService:
    pass


class CropRecommendationLLMService:
    llm = OpenAIClient(model=secrets['OPENAI_DEFAULT_MODEL'], temperature=0.0)
    crops_file = './data/africas-common-crops-curated-gpt-4o-2025-05-31.csv'

    @staticmethod
    def fuzzy_in_list(item, list_str):
        return any(fuzz.partial_ratio(item.lower(), loc.strip().lower()) > 80 for loc in list_str.split(','))

    @staticmethod
    def score_crop(row, input_params):
        score = 0

        # Extract input parameters
        input_soil_texture = input_params['soil_texture']
        input_ph = safe_float(input_params['soil_ph'])
        input_n = safe_float(input_params['soil_nitrogen_ppm'])
        input_p = safe_float(input_params['soil_phosphorus_ppm'])
        input_k = safe_float(input_params['soil_potassium_ppm'])
        input_om = safe_float(input_params['soil_organic_matter_pct'])
        input_temp = safe_float(input_params['soil_temperature'])
        input_location = input_params['country']

        # Soil Type Match (25%)
        if pd.notna(row['optimal_soil_type']) and input_soil_texture.lower() in row['optimal_soil_type'].lower():
            score += 25

        # pH Fit (20%)
        score += 20 * range_fit(input_ph, row['optimal_soil_ph_min'], row['optimal_soil_ph_max'])

        # Nutrient Match (20%) with weights
        nutrient_weights = {'nitrogen': 0.4, 'phosphorus': 0.3, 'potassium': 0.3}
        nutrient_score = (
            nutrient_weights['nitrogen'] * range_fit(input_n, row.get('optimal_soil_nitrogen_min', 0), row.get('optimal_soil_nitrogen_max', float('inf')))
            + nutrient_weights['phosphorus'] * range_fit(input_p, row.get('optimal_soil_phosphorus_min', 0), row.get('optimal_soil_phosphorus_max', float('inf')))
            + nutrient_weights['potassium'] * range_fit(input_k, row.get('optimal_soil_potassium_min', 0), row.get('optimal_soil_potassium_max', float('inf')))
        )
        score += 20 * nutrient_score

        # Organic Matter Fit (5%)
        if pd.notna(row['optimal_soil_organic_matter_min']) and input_om >= row['optimal_soil_organic_matter_min']:
            score += 5

        # Location Suitability (10%)
        if pd.notna(row['countries_grown']) and input_location:
            if CropRecommendationLLMService.fuzzy_in_list(input_location, row['countries_grown']):
                score += 10

        # Market Demand (5%)
        if pd.notna(row['demand_index']) and row['demand_index'] >= 0.75:
            score += 5

        # Temperature Fit (15%)
        score += 15 * range_fit(input_temp, row['optimal_temp_min'], row['optimal_temp_max'])

        return score

    def llm_refine_recommendation(self, crop_list, input_params):
        prompt_text = """
        You are an expert agronomist. Given the following farm conditions and initial crop suggestions, rank the crops by suitability for this farm. Consider:
        - Soil texture, pH, nutrients, moisture, organic matter, temperature
        - Country and local market demand
        - Resilience to climate and local risks (e.g., drought resistance, flood tolerance, heat tolerance)
        - Profitability and popularity in the country
        - {user_preferences}
        
        Farm Conditions:
        Soil Texture: {soil_texture}
        Soil pH: {soil_ph}
        Nitrogen: {soil_nitrogen_ppm} ppm
        Phosphorus: {soil_phosphorus_ppm} ppm
        Potassium: {soil_potassium_ppm} ppm
        Soil Moisture: {soil_moisture_pct}%
        Organic Matter: {soil_organic_matter_pct}%
        Temperature: {soil_temperature} °C
        Country: {country}
        
        Initial crop suggestions: {crop_list}
        
        For each crop, provide:
        - crop: name
        - suitability_score: integer 0-100 (higher is better)
        - explanation: 1-2 sentences on why this crop is suitable or not
        
        Return a JSON array, e.g.:
        [
          {{"crop": "Maize", "suitability_score": 92, "explanation": "Maize matches the soil and climate, and is in high demand locally."}},
          {{"crop": "Rice", "suitability_score": 75, "explanation": "Rice is suitable but less profitable in this region."}}
        ]
        """
        messages = [
            ("system", "You are an expert agronomist. Rank the crops for this farm and explain your reasoning. Output a JSON array as specified."),
            ("human", prompt_text)
        ]

        # user preferences
        if input_params['specified_preferences']:
            preferences_text = f"User preferences: {', '.join(input_params['specified_preferences'])}"

        prompt = ChatPromptTemplate(messages).invoke(
            {
                "soil_texture": input_params['soil_texture'],
                "soil_ph": input_params['soil_ph'],
                "soil_nitrogen_ppm": input_params['soil_nitrogen_ppm'],
                "soil_phosphorus_ppm": input_params['soil_phosphorus_ppm'],
                "soil_potassium_ppm": input_params['soil_potassium_ppm'],
                "soil_moisture_pct": input_params['soil_moisture_pct'],
                "soil_organic_matter_pct": input_params['soil_organic_matter_pct'],
                "soil_temperature": input_params['soil_temperature'],
                "country": input_params['country'],
                "user_preferences": preferences_text if input_params['specified_preferences'] else '',
                "crop_list": ', '.join(crop_list)
            }
        )
        return self.llm.prompt_chat_model(prompt)

    def get_crop_recommendations(self, input_params, top_n=5):
        try:
            # Convert input params to dictionary
            input_params = input_params.dict()

            # Load data
            crops_df = pd.read_csv(self.crops_file)

            # Apply scoring function
            crops_df['suitability_score'] = crops_df.apply(
                self.score_crop, axis=1, input_params=input_params)

            # Sort crops based on logic suitability
            top_crops = crops_df.sort_values(by='suitability_score', ascending=False)[['crop_id', 'crop', 'suitability_score']]

            # Use LLM to refine recommendations
            crop_list = top_crops['crop'].tolist()
            llm_crops = self.llm_refine_recommendation(crop_list, input_params)

            if isinstance(llm_crops, dict):
                llm_crops = [llm_crops]
            elif not isinstance(llm_crops, list):
                raise ValueError("llm_refine_recommendation must return a list of dicts or a dict")

            llm_crops = pd.DataFrame(llm_crops)

            # Merge LLM recommendations with initial recommendations
            top_crops = top_crops.merge(llm_crops, on='crop', how='left')

            # replace NaN with 0 in suitability_score_x and suitability_score_y
            top_crops['suitability_score_x'] = top_crops['suitability_score_x'].fillna(0) / 100
            top_crops['suitability_score_y'] = top_crops['suitability_score_y'].fillna(0) / 100

            # final score = 0.3 * suitability_score + 0.7 * llm_score
            top_crops['suitability_score'] = 0.3 * top_crops['suitability_score_x'] + 0.7 * top_crops['suitability_score_y']

            # sort by suitability score
            top_crops = top_crops.sort_values(by='suitability_score', ascending=False)
            
            # select and rename columns for output
            top_crops = top_crops[['crop_id', 'crop', 'suitability_score_x', 'suitability_score_y', 'suitability_score', 'explanation']]
            top_crops = top_crops.rename(columns={
                'suitability_score_x': 'logic_suitability_score',
                'suitability_score_y': 'llm_suitability_score',
                'crop': 'crop_name'
            })

            # Fill nan values in explanation with default text
            top_crops['explanation'] = top_crops['explanation'].fillna('No explanation available')

            # select top N crops
            top_crops = top_crops.head(top_n)

            return schema.CropRecommendationList(
                recommendations=[
                    schema.CropRecommendation(
                        crop_id=row['crop_id'],
                        crop_name=row['crop_name'],
                        suitability_score=round(row['suitability_score'], 3),
                        logic_suitability_score=round(row['logic_suitability_score'], 3),
                        llm_suitability_score=round(row['llm_suitability_score'], 3),
                        explanation=row['explanation']
                    ) for _, row in top_crops.iterrows()
                ],
                total=len(top_crops)
            )
        except Exception as e:
            logger.error(f"Error getting crop recommendations: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error getting crop recommendations"
            )
        