from pydantic import BaseModel, Field


class TextToSpeechRequest(BaseModel):
    text: str = Field(..., description="The text to be converted to speech.")
    voice_name: str = Field("en-US-Wavenet-C", description="The name of the voice to use (e.g., 'en-US-Wavenet-D').")
    language_code: str = Field("en-US", description="The language code (e.g., 'en-US').")
    
    
class TextToSpeechResponse(BaseModel):
    audio_stream: bytes = Field(..., description="The audio stream of the synthesized speech.")
