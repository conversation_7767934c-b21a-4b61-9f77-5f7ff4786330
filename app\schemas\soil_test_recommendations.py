from enum import Enum
from typing import List, Optional
from pydantic import BaseModel


class SoilNutrientRating(str, Enum):
    VERY_LOW = "Very Low"
    LOW = "Low"
    MODERATE = "Moderate"
    HIGH = "High"
    VERY_HIGH = "Very High"


class SoilTestData(BaseModel):
    soil_texture: Optional[str] = None
    soil_moisture_pct: Optional[float] = None
    soil_temperature: Optional[float] = None
    soil_ec: Optional[float] = None
    soil_ph: Optional[float] = None
    soil_nitrogen_ppm: Optional[float] = None
    soil_phosphorus_ppm: Optional[float] = None
    soil_potassium_ppm: Optional[float] = None
    soil_calcium_ppm: Optional[float] = None
    soil_magnesium_ppm: Optional[float] = None
    soil_sulfur_ppm: Optional[float] = None
    soil_organic_matter_pct: Optional[float] = None
    country: Optional[str] = None

    class Config:
        from_attributes = True


class SoilTestRecommendation(BaseModel):
    soil_fertility: dict
    soil_amendments: List[str]
    crop_recommendations: List[dict]
    fertilizer_recommendations: List[str]
    application_rates: List[str]
    application_methods: List[str]
    timing: str
    soil_nutrient_ratings: dict
    additional_notes: Optional[str] = None

    class Config:
        from_attributes = True
