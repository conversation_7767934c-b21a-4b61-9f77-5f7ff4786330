from mangum import Mangum
from fastapi import FastAPI, APIRouter
from starlette.responses import RedirectResponse

from config import secrets, init_sentry
from routes.auth import router as auth_router
from routes.ml import router as ml_router
from routes.llm import router as llm_router
from routes.chat import router as chat_router, lifespan
from routes.weather import router as weather_router

# Initialize Sentry
init_sentry()

app = FastAPI(
    title=f"CropSense ML API",
    description=f"CropSense ML Inference API - {secrets['ENV'].upper()} Environment",
    version="v0.1.0",
    contact={
        "name": "CropSense Africa",
        "email": "<EMAIL>",
        "url": "https://cropsense.africa/"
    },
    root_path=f"/{secrets['ENV']}/" if secrets['ENV'] not in ['dev', 'prod'] else "/",
    lifespan=lifespan
)
router = APIRouter()


@router.get("/")
async def root():
    return RedirectResponse("/docs")

app.include_router(router, include_in_schema=False)
app.include_router(auth_router, prefix="/auth", tags=["Auth"])
app.include_router(ml_router, prefix="/ml", tags=["Agro-ML"])
app.include_router(llm_router, prefix="/llm", tags=["Agro-LLM"])
app.include_router(chat_router, prefix="/chat", tags=["Chat"])
app.include_router(weather_router, prefix="/weather", tags=["Weather"])


handler = Mangum(app)

if __name__ == '__main__':
    import uvicorn
    uvicorn.run(app, port=8000, host='0.0.0.0')
