output "api_url" {
  value = aws_api_gateway_deployment.deployment.invoke_url
}

output "ecr_repository_url" {
  value = aws_ecr_repository.cropsense-backend-ml-api.repository_url
}

output "aws_lambda_function_url_prod" {
  value = aws_lambda_function_url.cropsense_backend_ml_api_lambda_prod.function_url
}

output "aws_lambda_function_url_staging" {
  value = aws_lambda_function_url.cropsense_backend_ml_api_lambda_staging.function_url
}
