import langchain
from fastapi import HTTPException, status
from langchain_core.prompts import ChatPromptT<PERSON>plate

from config import secrets, logger
from utils.llm_openai import OpenAIClient
import schemas.activity_recommedation as schema

# config
langchain.verbose = False
langchain.debug = False
langchain.llm_cache = False


class ActivityRecommendationLLMService:
    llm = OpenAIClient(
        model=secrets['OPENAI_DEFAULT_MODEL'],
        temperature=0.0
    )

    def activity_recommendation_llm_prompt(self, data: schema.ActivityRecommendationRequest):
        """Prompt the LLM with activity recommendation data (farm context + activity only)"""

        system_prompt = """
    You are an expert agricultural advisor with deep knowledge of crop science, soil health, climate-smart farming, and precision agriculture.

    You will be given:
    1. A detailed **farm context**.
    2. A list of **recent or ongoing farm activities**.

    Your task is to analyze the provided context and activities and recommend the next best actions the farmer should take.

    Your response **MUST** be a well-formed JSON object. The object must have a single root key called "recommendations". The value of "recommendations" must be an array of JSON objects.

    Each object in the "recommendations" array must contain exactly two keys:
    1.  `"activity"`: A string  from the list of Planting, Irrigation, Pest control, Havesting, Weeding ** must not be anything else **.
    2.  `"reason"`: A string providing a detailed justification for why this activity is the ideal next step, considering the farm's context, timing, and the goal of maximizing crop productivity and farm health.

    ---
    **EXAMPLE:**

    **Farm Context (Input):**
    - Location: Central Iowa, 200 acres
    - Soil Type: Clay Loam, pH 6.8, Organic Matter 3.5%
    - Crop Type: Corn, currently at the R1 (silking) growth stage
    - Weather: Forecast shows a 70% chance of rain (0.5 inches) in 2 days
    - Pest/Disease Issues: Minor leaf yellowing observed on lower leaves in several sections of the field.
    - Historical Challenges: Yields have been limited by nitrogen availability late in the season.

    **Recent or Ongoing Farm Activities (Input):**
    - Just completed a post-emergence herbicide application last week.
    - Irrigation is available but not currently running due to adequate soil moisture.

    **Expected JSON Output:**
    ```json
    {
  "recommendations": [
    {
      "activity": "Pest control",
      "reason": "The observed leaf yellowing indicates a potential crop health issue. The next logical step under pest and disease management is to accurately diagnose the cause. A tissue test should be conducted to confirm if this is a nutrient deficiency, like Nitrogen, before any treatment is planned. This prevents unnecessary applications and targets the specific problem."
    },
    {
      "activity": "Irrigation",
      "reason": "The corn is at the R1 silking stage, a critical period where water stress can significantly reduce yield. While rain is forecasted, it is not guaranteed. Therefore, the irrigation system should be prepared to run. This ensures that if the rain is insufficient, the crop's high water demand is met, maximizing nutrient uptake and grain fill."
    }
  ]
}
    ```
    """

        human_prompt = """
    Farm Context:
    {farm_context}

    Recent or Ongoing Farm Activities:
    {farm_activity}
    """

        messages = [
            ("system", system_prompt.strip()),
            ("human", human_prompt.strip())
        ]

        prompt = ChatPromptTemplate.from_messages(messages).invoke({
            "farm_context": data.farm_context,
            "farm_activity": data.farm_activity
        })
        return self.llm.prompt_chat_model(prompt)
  

    def get_activity_recommendation(self, data: schema.ActivityRecommendationRequest):
        """Get activity recommendation"""
        try:
            response = self.activity_recommendation_llm_prompt(data)
            return schema.ActivityRecommendationResponse(
                recommendations=response['recommendations']
            )
        except Exception as e:
            logger.error(f"Error getting activity recommendation: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error getting activity recommendation"
            )