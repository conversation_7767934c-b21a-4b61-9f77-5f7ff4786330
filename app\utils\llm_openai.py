import json
from config import <PERSON><PERSON><PERSON><PERSON>
from langchain_openai import ChatOpenAI


# Load env secrets
secrets = SecretsManager().get_secrets()


class OpenAIClient:
    """ OpenAIClient class for interacting with the OpenAI API. """

    def __init__(self, model: str = None, temperature: float = 0.0):
        """
        Initializes the OpenAIClient by loading environment variables
        and setting up the OpenAI API client.
        """
        self.api_key = secrets['OPENAI_API_KEY']
        self.model = model if model else "gpt-4.1-nano"
        self.temperature = temperature

    def __str__(self):
        return f"OpenAIClient(model={self.model}, temperature={self.temperature})"

    def prompt_chat_model(self, prompt: str, output_format: str = "json"):
        """
        Prompts the chat model with the provided prompt.

        :param prompt: The prompt to send to the chat model.
        :param output_format: The output format for the response.
        :return: The response from the chat model.

        NOTE: Use this method for all models except GP-3.5-turbo-instruct
        """
        model = ChatOpenAI(
            api_key=self.api_key,
            model=self.model,
            temperature=self.temperature
        )
        response = model.invoke(prompt)
        response = response.content

        if output_format != "json":
            return response

        if "```json\n" in response:
            response = response.split("```json\n")[1]

        if "\n```" in response:
            response = response.split("\n```")[0]

        return json.loads(response)
