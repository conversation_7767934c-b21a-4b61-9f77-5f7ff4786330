import uuid
from enum import Enum
from pydantic import BaseModel, Field, field_validator
from typing import Optional, Literal, List, Dict, Any
from datetime import datetime, timezone


SUPPORTED_LLM_PROVIDERS = Literal["gemini", "openai", "llama", "deepseek"]


class Channels(str, Enum):
    WEB = "web"
    MOBILE = "mobile"
    WHATSAPP = "whatsapp"


class RagQueryRequest(BaseModel):
    """Request model for querying the RAG system."""
    question: str = Field(..., description="The question to ask the RAG system.", min_length=1)
    farm_context: dict = Field(..., description="The farm context to use for generating the answer.")
    rag: bool = Field(False, description="Whether to use the RAG system to generate the answer.")
    language: str = Field("English", description="The language the user is using. If the answer is in a language other than English, the language should be specified here.")
    llm_provider: Optional[SUPPORTED_LLM_PROVIDERS] = Field(
        "gemini", # fastest response time compared to others
        description="The LLM provider to use for generating the answer (e.g., 'gemini', 'openai', 'llama', 'deepseek'). Context is always retrieved from the main text-based vector store.",
    )
    channel_id: Optional[str] = Field(
        "web", 
        description="Unique identifier for the channel to maintain conversation context."
    )
    conversation_id: Optional[str] = Field(
        None,
        description="Optional conversation ID. If not provided, will use the most recent conversation or create a new one."
    )
    
    @field_validator('llm_provider')
    @classmethod
    def provider_to_lowercase(cls, value: str) -> str:
        return value.lower()


class RagQueryResponse(BaseModel):
    """Response model for the RAG system query."""
    question: str = Field(description="The original question asked.")
    answer: str = Field(None, description="The generated answer based on the retrieved context. Will be null if an error occurred or no answer could be found.")
    language: str = Field(None, description="The language the answer is in. Will be null if the answer is in English.")
    llm_provider_used: str = Field(description="The LLM provider that generated the answer.")
    channel_id: str = Field(description="The channel ID associated with this conversation.")
    conversation_id: str = Field(description="The conversation ID associated with this interaction.")
    message_id: str = Field(description="The ID of the assistant's response message.")

    model_config = {
        "from_attributes": True, 
        "json_schema_extra": {
             "examples": [
                 {
                     "question": "How to plant coffee seedlings?",
                     "answer": "Based on the provided documents, coffee seedlings should be planted during the rainy season...",
                     "language": "English",
                     "llm_provider_used": "gemini",
                     "channel_id": "channel123",
                     "conversation_id": "conv123",
                     "message_id": "msg123"
                 },
                 {
                     "question": "What is the price of gold?",
                     "answer": "Based on the provided documents, I cannot answer that question.", # LLM might return this
                     "language": "English",
                     "llm_provider_used": "openai",
                     "channel_id": "channel123",
                     "conversation_id": "conv123",
                     "message_id": "msg124"
                 },
                  {
                     "question": "How to fertilize?",
                     "answer": None, # Answer is None when an error occurs server-side
                     "language": "English",
                     "llm_provider_used": "llama",
                     "channel_id": "channel123",
                     "conversation_id": "conv123",
                     "message_id": "msg125"
                 }
             ]
         }
    }


class Message(BaseModel):
    """Represents a single message in a conversation."""
    message_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique identifier for the message")
    conversation_id: str = Field(..., description="The conversation this message belongs to")
    role: str = Field(..., description="The role of the message sender (e.g., 'user' or 'assistant')")
    content: str = Field(..., description="The content of the message")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When the message was created")
    is_liked: bool = Field(default=False, description="Whether the message was liked by the user")
    is_disliked: bool = Field(default=False, description="Whether the message was disliked by the user")


class Conversation(BaseModel):
    """Represents a conversation with a user."""
    conversation_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique identifier for the conversation")
    user_id: str = Field(..., description="Unique identifier for the user")
    channel_id: str = Field(..., description="Unique identifier for the channel where the conversation takes place")
    title: Optional[str] = Field(None, description="Optional title for the conversation")
    is_active: bool = Field(default=True, description="Whether this is the active conversation for the user")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When the conversation was created")
    last_updated: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When the conversation was last updated")
    
    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "conversation_id": "conv123",
                    "user_id": "user123",
                    "channel_id": "channel456",
                    "title": "Coffee Planting Discussion",
                    "is_active": True,
                    "created_at": "2024-03-20T10:00:00",
                    "last_updated": "2024-03-20T10:00:05"
                }
            ]
        }
    }


class MessageFeedbackRequest(BaseModel):
    """Request model for providing feedback on a message."""
    message_id: str = Field(..., description="The ID of the message to provide feedback on")
    feedback: Literal["like", "dislike"] = Field(..., description="The feedback to provide. Must be either 'like' or 'dislike'.")


class MessageFeedbackResponse(BaseModel):
    """Response model for providing feedback on a message."""    
    success: bool = Field(..., description="Whether the message was successfully liked or disliked.")    
    message_id: str = Field(..., description="The ID of the message that was liked or disliked.")
    conversation_id: str = Field(..., description="The conversation ID the message belongs to")
    feedback: Literal["like", "dislike"] = Field(..., description="The feedback provided. Must be either 'like' or 'dislike'.")    
    

class ClearConversationRequest(BaseModel):
    """Request model for clearing conversation history."""
    conversation_id: str = Field(..., description="The conversation ID to clear.")


class ClearConversationResponse(BaseModel):
    """Response model for clearing conversation history."""
    success: bool = Field(description="Whether the conversation was successfully cleared.")
    conversation_id: str = Field(description="The conversation ID that was cleared.")
    user_id: str = Field(description="The user ID whose conversation was cleared.")
    channel_id: str = Field(description="The channel ID whose conversation was cleared.")
    message: str = Field(description="A message describing the result of the operation.")


class CreateConversationRequest(BaseModel):
    """Request model for creating a new conversation."""
    channel_id: str = Field(..., description="The channel ID to create the conversation in.")
    title: Optional[str] = Field(None, description="Optional title for the conversation")


class CreateConversationResponse(BaseModel):
    """Response model for creating a new conversation."""
    success: bool = Field(description="Whether the conversation was successfully created.")
    conversation_id: str = Field(description="The ID of the newly created conversation.")
    user_id: str = Field(description="The user ID the conversation was created for.")
    channel_id: str = Field(description="The channel ID the conversation was created in.")
    title: Optional[str] = Field(None, description="The title of the conversation, if provided.")


class ConversationHistoryResponse(BaseModel):
    """Response model for retrieving conversation history."""
    conversation_id: str = Field(description="The ID of the conversation.")
    history: List[Message] = Field(description="The list of messages in the conversation.")


class UserConversationsResponse(BaseModel):
    """Response model for retrieving all conversations for a user."""
    conversations: List[Conversation] = Field(description="The list of conversations for the user.")
