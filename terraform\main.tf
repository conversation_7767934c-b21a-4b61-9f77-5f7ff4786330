terraform {
  backend "s3" {
    bucket = "ciphersense-ai-prod-infra-tfstate"
    key    = "cropsense/ml-api/terraform.tfstate"
    region = "eu-central-1"
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = "eu-central-1"
}

##############################
# ECR Repository for the Image
##############################
resource "aws_ecr_repository" "cropsense-backend-ml-api" {
  name = "cropsense-backend-ml-api"

  image_scanning_configuration {
    scan_on_push = true
  }
}

resource "aws_ecr_lifecycle_policy" "cropsense-backend-ml-api" {
  repository = aws_ecr_repository.cropsense-backend-ml-api.name

  policy = jsonencode({
    rules = [
      {
        rulePriority = 1
        description  = "Keep only the last 3 images"
        selection = {
          tagStatus   = "any"
          countType   = "imageCountMoreThan"
          countNumber = 3
        }
        action = {
          type = "expire"
        }
      }
    ]
  })
}

##################################################
# IAM Role for Lambda Function (with Basic Policy)
##################################################
# IAM Role for Lambda Function
resource "aws_iam_role" "cropsense_ml_lambda_exec_role" {
  name = "cropsense_ml_lambda_exec_role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Action = "sts:AssumeRole",
      Effect = "Allow",
      Principal = {
        Service = "lambda.amazonaws.com"
      }
    }]
  })
}

# Basic Lambda execution policy
resource "aws_iam_role_policy_attachment" "cropsense_ml_lambda_exec_role_policy" {
  role       = aws_iam_role.cropsense_ml_lambda_exec_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

#########################################################
# AWS Secrets Manager for storing the secrets - STAGING
#########################################################
resource "aws_secretsmanager_secret" "cropsense_backend_ml_api_secrets_staging" {
  name = "staging/cropsense-backend-ml-api-secrets"
}

resource "aws_iam_policy" "cropsense_backend_ml_api_secrets_policy_staging" {
  name        = "cropsense-backend-ml-api-secrets-policy-staging"
  description = "Allows access to Secrets Manager"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Effect   = "Allow",
      Action   = "secretsmanager:GetSecretValue",
      Resource = aws_secretsmanager_secret.cropsense_backend_ml_api_secrets_staging.arn
    }]
  })

  depends_on = [aws_secretsmanager_secret.cropsense_backend_ml_api_secrets_staging]
}

resource "aws_iam_role_policy_attachment" "cropsense_backend_ml_api_secrets_policy_attachment_staging" {
  role       = aws_iam_role.cropsense_ml_lambda_exec_role.name
  policy_arn = aws_iam_policy.cropsense_backend_ml_api_secrets_policy_staging.arn
}

resource "aws_secretsmanager_secret_version" "cropsense_backend_ml_api_secrets_version_staging" {
  secret_id = aws_secretsmanager_secret.cropsense_backend_ml_api_secrets_staging.id
  secret_string = jsonencode({
    "ENV"                           = var.ENV,
    "DB_URI"                        = var.DB_URI,
    "DB_SCHEMA"                     = var.DB_SCHEMA,
    "AWS_SECRET_ACCESS_KEY"         = var.AWS_SECRET_ACCESS_KEY,
    "AWS_ACCESS_KEY_ID"             = var.AWS_ACCESS_KEY_ID,
    "AWS_REGION"                    = var.AWS_REGION,
    "JWT_SECRET_KEY"                = var.JWT_SECRET_KEY,
    "JWT_REFRESH_SECRET_KEY"        = var.JWT_REFRESH_SECRET_KEY,
    "OPENAI_API_KEY"                = var.OPENAI_API_KEY,
    "OPENAI_DEFAULT_MODEL"          = var.OPENAI_DEFAULT_MODEL,
    "GEMINI_API_KEY"                = var.GEMINI_API_KEY,
    "GEMINI_DEFAULT_MODEL"          = var.GEMINI_DEFAULT_MODEL,
    "DEEPSEEK_API_KEY"              = var.DEEPSEEK_API_KEY,
    "DEEPSEEK_DEFAULT_MODEL"        = var.DEEPSEEK_DEFAULT_MODEL,
    "GROK_API_KEY"                  = var.GROK_API_KEY,
    "LLAMA_DEFAULT_MODEL"           = var.LLAMA_DEFAULT_MODEL,
    "AWS_S3_BUCKET"                 = var.AWS_S3_BUCKET,
    "SENTRY_DSN"                    = var.SENTRY_DSN,
    "MONGODB_URI"                   = var.MONGODB_URI,
    "GOOGLE_MAPS_API_KEY"           = var.GOOGLE_MAPS_API_KEY,
    "OPENWEATHERMAP_API_KEY"        = var.OPENWEATHERMAP_API_KEY,
    "GOOGLE_TEXT_TO_SPEECH_API_KEY" = var.GOOGLE_TEXT_TO_SPEECH_API_KEY
  })
}

#########################################################
# AWS Secrets Manager for storing the secrets - PROD
#########################################################
resource "aws_secretsmanager_secret" "cropsense_backend_ml_api_secrets_prod" {
  name = "prod/cropsense-backend-ml-api-secrets"
}

resource "aws_iam_policy" "cropsense_backend_ml_api_secrets_policy_prod" {
  name        = "cropsense-backend-ml-api-secrets-policy-prod"
  description = "Allows access to Secrets Manager"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Effect   = "Allow",
      Action   = "secretsmanager:GetSecretValue",
      Resource = aws_secretsmanager_secret.cropsense_backend_ml_api_secrets_prod.arn
    }]
  })

  depends_on = [aws_secretsmanager_secret.cropsense_backend_ml_api_secrets_prod]
}

resource "aws_iam_role_policy_attachment" "cropsense_backend_ml_api_secrets_policy_attachment_prod" {
  role       = aws_iam_role.cropsense_ml_lambda_exec_role.name
  policy_arn = aws_iam_policy.cropsense_backend_ml_api_secrets_policy_prod.arn
}

resource "aws_secretsmanager_secret_version" "cropsense_backend_ml_api_secrets_version_prod" {
  secret_id = aws_secretsmanager_secret.cropsense_backend_ml_api_secrets_prod.id
  secret_string = jsonencode({
    "ENV"                           = "prod",
    "DB_URI"                        = var.DB_URI,
    "DB_SCHEMA"                     = "prod",
    "AWS_SECRET_ACCESS_KEY"         = var.AWS_SECRET_ACCESS_KEY,
    "AWS_ACCESS_KEY_ID"             = var.AWS_ACCESS_KEY_ID,
    "AWS_REGION"                    = var.AWS_REGION,
    "JWT_SECRET_KEY"                = var.JWT_SECRET_KEY,
    "JWT_REFRESH_SECRET_KEY"        = var.JWT_REFRESH_SECRET_KEY,
    "OPENAI_API_KEY"                = var.OPENAI_API_KEY,
    "OPENAI_DEFAULT_MODEL"          = var.OPENAI_DEFAULT_MODEL,
    "GEMINI_API_KEY"                = var.GEMINI_API_KEY,
    "GEMINI_DEFAULT_MODEL"          = var.GEMINI_DEFAULT_MODEL,
    "DEEPSEEK_API_KEY"              = var.DEEPSEEK_API_KEY,
    "DEEPSEEK_DEFAULT_MODEL"        = var.DEEPSEEK_DEFAULT_MODEL,
    "GROK_API_KEY"                  = var.GROK_API_KEY,
    "LLAMA_DEFAULT_MODEL"           = var.LLAMA_DEFAULT_MODEL,
    "AWS_S3_BUCKET"                 = var.AWS_S3_BUCKET,
    "SENTRY_DSN"                    = var.SENTRY_DSN,
    "MONGODB_URI"                   = var.MONGODB_URI,
    "GOOGLE_MAPS_API_KEY"           = var.GOOGLE_MAPS_API_KEY,
    "OPENWEATHERMAP_API_KEY"        = var.OPENWEATHERMAP_API_KEY,
    "GOOGLE_TEXT_TO_SPEECH_API_KEY" = var.GOOGLE_TEXT_TO_SPEECH_API_KEY
  })
}

##################################################
# Lambda Layer for Python dependencies (from requirements.txt)
##################################################

/*
resource "null_resource" "install_layer_dependencies" {
  triggers = {
    trigger = filemd5("../requirements.txt")
  }

  provisioner "local-exec" {
    command = "mkdir -p ../artefacts/layer/python/lib/${var.runtime}/site-packages"
  }
  provisioner "local-exec" {
    command = "pip install -r ../requirements.txt -t ../artefacts/layer/python/lib/${var.runtime}/site-packages"
  }
}

data "archive_file" "lambda_source_layer" {
  type        = "zip"
  source_dir  = "../artefacts/layer"
  output_path = "../artefacts/cropsense_backend_ml_api_lambda_layer.zip"

  depends_on = [null_resource.install_layer_dependencies]
}

resource "aws_s3_object" "upload_lambda_layer" {
  bucket = "ciphersense-ai-prod-infra-tfstate"
  key    = "cropsense/ml-api/lambda_layers/cropsense_backend_ml_api_lambda_layer.zip"
  source = data.archive_file.lambda_source_layer.output_path
}

resource "aws_lambda_layer_version" "etl_layer" {
  layer_name = "cropsense-backend-ml-api-layer"

  s3_bucket = "ciphersense-ai-prod-infra-tfstate"
  s3_key    = "cropsense/ml-api/lambda_layers/cropsense_backend_ml_api_lambda_layer.zip"

  source_code_hash    = data.archive_file.lambda_source_layer.output_base64sha256
  compatible_runtimes = [var.runtime]

  depends_on = [
    data.archive_file.lambda_source_layer,
    aws_s3_object.upload_lambda_layer
  ]
}
*/

##################################################
# Lambda Function for the ML API (Python 3.11) - Staging
##################################################

# NOTE: Ensure that the image is built and pushed to the ECR repository before running this script.
resource "aws_lambda_function" "cropsense_backend_ml_api_lambda_staging" {
  function_name = "cropsense-backend-ml-api-lambda-staging"
  role          = aws_iam_role.cropsense_ml_lambda_exec_role.arn
  package_type  = "Image"

  # Replace ":latest" with your desired tag if needed.
  image_uri = "${aws_ecr_repository.cropsense-backend-ml-api.repository_url}:latest"

  timeout     = 60
  memory_size = 512

  environment {
    variables = {
      ENV                           = var.ENV,
      DB_URI                        = var.DB_URI
      DB_SCHEMA                     = var.DB_SCHEMA
      JWT_SECRET_KEY                = var.JWT_SECRET_KEY
      JWT_REFRESH_SECRET_KEY        = var.JWT_REFRESH_SECRET_KEY
      OPENAI_API_KEY                = var.OPENAI_API_KEY
      OPENAI_DEFAULT_MODEL          = var.OPENAI_DEFAULT_MODEL,
      GEMINI_API_KEY                = var.GEMINI_API_KEY,
      GEMINI_DEFAULT_MODEL          = var.GEMINI_DEFAULT_MODEL,
      DEEPSEEK_API_KEY              = var.DEEPSEEK_API_KEY,
      DEEPSEEK_DEFAULT_MODEL        = var.DEEPSEEK_DEFAULT_MODEL,
      GROK_API_KEY                  = var.GROK_API_KEY,
      LLAMA_DEFAULT_MODEL           = var.LLAMA_DEFAULT_MODEL,
      AWS_S3_BUCKET                 = var.AWS_S3_BUCKET,
      SENTRY_DSN                    = var.SENTRY_DSN,
      MONGODB_URI                   = var.MONGODB_URI,
      GOOGLE_TEXT_TO_SPEECH_API_KEY = var.GOOGLE_TEXT_TO_SPEECH_API_KEY
    }
  }
}

resource "aws_lambda_function_url" "cropsense_backend_ml_api_lambda_staging" {
  function_name      = aws_lambda_function.cropsense_backend_ml_api_lambda_staging.function_name
  authorization_type = "NONE"

  cors {
    allow_credentials = true
    allow_origins     = ["*"] # You may want to restrict this to specific origins in production
    allow_methods     = ["*"] # This allows all methods in a single entry
    allow_headers     = ["*"]
    expose_headers    = ["*"]
    max_age           = 86400
  }
}

##################################################
# Lambda Function for the ML API (Python 3.11) - Prod
##################################################

resource "aws_lambda_function" "cropsense_backend_ml_api_lambda_prod" {
  function_name = "cropsense-backend-ml-api-lambda-prod"
  role          = aws_iam_role.cropsense_ml_lambda_exec_role.arn
  package_type  = "Image"

  # Replace ":latest" with your desired tag if needed.
  image_uri = "${aws_ecr_repository.cropsense-backend-ml-api.repository_url}:latest"

  timeout     = 60
  memory_size = 1024

  environment {
    variables = {
      ENV                           = "prod",
      DB_URI                        = var.DB_URI
      DB_SCHEMA                     = "prod"
      JWT_SECRET_KEY                = var.JWT_SECRET_KEY
      JWT_REFRESH_SECRET_KEY        = var.JWT_REFRESH_SECRET_KEY
      OPENAI_API_KEY                = var.OPENAI_API_KEY
      OPENAI_DEFAULT_MODEL          = var.OPENAI_DEFAULT_MODEL,
      GEMINI_API_KEY                = var.GEMINI_API_KEY,
      GEMINI_DEFAULT_MODEL          = var.GEMINI_DEFAULT_MODEL,
      DEEPSEEK_API_KEY              = var.DEEPSEEK_API_KEY,
      DEEPSEEK_DEFAULT_MODEL        = var.DEEPSEEK_DEFAULT_MODEL,
      GROK_API_KEY                  = var.GROK_API_KEY,
      LLAMA_DEFAULT_MODEL           = var.LLAMA_DEFAULT_MODEL,
      AWS_S3_BUCKET                 = var.AWS_S3_BUCKET,
      SENTRY_DSN                    = var.SENTRY_DSN,
      MONGODB_URI                   = var.MONGODB_URI,
      GOOGLE_TEXT_TO_SPEECH_API_KEY = var.GOOGLE_TEXT_TO_SPEECH_API_KEY
    }
  }
}

resource "aws_lambda_function_url" "cropsense_backend_ml_api_lambda_prod" {
  function_name      = aws_lambda_function.cropsense_backend_ml_api_lambda_prod.function_name
  authorization_type = "NONE"

  cors {
    allow_credentials = true
    allow_origins     = ["*"] # You may want to restrict this to specific origins in production
    allow_methods     = ["*"] # This allows all methods in a single entry
    allow_headers     = ["*"]
    expose_headers    = ["*"]
    max_age           = 86400
  }
}

##################################################
# API Gateway for the ML API - Prod
##################################################
resource "aws_api_gateway_rest_api" "cropsense_backend_ml_api" {
  name        = "cropsense-backend-ml-api"
  description = "API Gateway for the CropSense ML Inference API"
}

resource "aws_api_gateway_resource" "proxy" {
  rest_api_id = aws_api_gateway_rest_api.cropsense_backend_ml_api.id
  parent_id   = aws_api_gateway_rest_api.cropsense_backend_ml_api.root_resource_id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "proxy" {
  rest_api_id   = aws_api_gateway_rest_api.cropsense_backend_ml_api.id
  resource_id   = aws_api_gateway_resource.proxy.id
  http_method   = "ANY"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "lambda" {
  rest_api_id = aws_api_gateway_rest_api.cropsense_backend_ml_api.id
  resource_id = aws_api_gateway_resource.proxy.id
  http_method = aws_api_gateway_method.proxy.http_method

  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = aws_lambda_function.cropsense_backend_ml_api_lambda_prod.invoke_arn
}

resource "aws_api_gateway_deployment" "deployment" {
  depends_on  = [aws_api_gateway_integration.lambda]
  rest_api_id = aws_api_gateway_rest_api.cropsense_backend_ml_api.id
  stage_name  = var.ENV # TODO: Replace with aws_api_gateway_stage resource
}

/*
resource "aws_api_gateway_stage" "stage" {
  rest_api_id = aws_api_gateway_rest_api.cropsense_backend_ml_api.id
  stage_name  = var.ENV
  deployment_id = aws_api_gateway_deployment.deployment.id
}
*/

# Lambda function permission to invoke the API Gateway
resource "aws_lambda_permission" "apigw_lambda" {
  statement_id  = "AllowExecutionFromAPIGateway"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.cropsense_backend_ml_api_lambda_prod.function_name
  principal     = "apigateway.amazonaws.com"

  source_arn = "${aws_api_gateway_deployment.deployment.execution_arn}/*/*"
}
