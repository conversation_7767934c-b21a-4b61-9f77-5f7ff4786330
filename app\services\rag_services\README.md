# Retrieval-Augmented Generation (RAG) Services

This module implements the core infrastructure for Retrieval-Augmented Generation (RAG) in the CropSense ML API. It provides robust configuration, vector store management, conversation memory, and the main RAG pipeline, integrating multiple LLM providers and supporting scalable, production-grade document retrieval and generation workflows.

---

## 📚 Overview

**RAG (Retrieval-Augmented Generation)** combines the power of information retrieval from curated knowledge bases with generative AI models. This enables:

- Accurate, contextually grounded responses
- Up-to-date information leveraging both structured and unstructured data
- Scalable, auditable, and extensible advisory systems

The services in this folder are designed for:

- Agricultural advisory chatbots
- Context-aware question answering
- Any workflow requiring LLMs to be grounded in domain-specific data

---

## 🗂️ Module Structure & Responsibilities

### `core_rag_service.py`

- **Purpose:** Implements the main RAG pipeline and conversation memory management.
- **Key Components:**
  - `ConversationMemoryService`: Manages chat history and conversation memory using MongoDB. Handles creation, retrieval, and deactivation of conversations per user/channel. Stores and retrieves messages, normalizes timestamps, and generates conversation titles. Provides feedback and context windowing for conversations.
  - `RAGService`: Orchestrates the RAG workflow: retrieval, prompt construction, LLM invocation, and response formatting. Integrates with multiple LLM providers (Google Gemini, OpenAI, DeepSeek, Groq(Llama)) via LangChain. Handles farm advisory prompt templates, document formatting, and context management. Manages conversation lifecycle and feedback. Provides a `query` method for running the full RAG pipeline, and utility methods for initialization and cleanup.
  - `get_rag_service`: Factory function to instantiate and return a configured `RAGService`.

### `vectorstore_manager_service.py`

- **Purpose:** Manages all operations related to vector stores (e.g., FAISS), document processing, and S3 integration.
- **Key Components:**
  - `S3Manager`: Handles upload/download of vector store files to/from AWS S3. Checks for file existence and manages S3 keys.
  - `EmbeddingFactory`: Creates embedding models (OpenAI or HuggingFace) for document vectorization, with fallback logic.
  - `StoreInfo`, `S3Keys`: Data classes for encapsulating vector store and S3 metadata.
  - `VectorStoreFactory`: Creates and loads FAISS vector stores from documents.
  - `StoreInfoFactory`: Utility for generating `StoreInfo` objects for different collections.
  - `DocumentProcessor`: Loads and splits documents from local or S3 sources. Supports text and PDF document types.
  - `VectorStoreManager`: High-level manager for vector store lifecycle. Provides retrievers for text and PDF, handles store creation/loading, and manages local/S3 persistence.

### `rag_config.py`

- **Purpose:** Defines configuration classes and loads all RAG-related settings from environment variables and secrets.
- **Key Components:**
  - `S3Config`: Holds S3-specific configuration (endpoint, credentials, bucket names, prefixes).
  - `Settings`: Loads and validates all application settings, including S3, embedding models, vector store backend, LLM provider/model, and API keys. Sets environment variables for downstream integrations. Performs post-initialization validation and logging.
  - `settings`: A singleton instance of `Settings` for use throughout the RAG services.

### `__init__.py`

- Marks the directory as a Python package.

---

## 🔒 Security & Configuration

- **Secrets & API Keys:** All credentials are loaded from environment variables or a secure secrets manager. Never hard-code secrets.
- **Input Validation:** All user and API inputs are validated and sanitized before processing.
- **Error Handling:** Robust error handling and logging are implemented throughout.
- **S3 & MongoDB:** Ensure proper IAM roles and network security for cloud resources.

---

## 📝 Best Practices

- **Environment Variables:** Store all sensitive configuration in environment variables or secret vaults.
- **Documentation:** Update this README and inline docstrings when making changes.
- **Code Quality:** Use linters (e.g., flake8) and formatters (e.g., Black) to enforce code style.

---

## 👥 Contributors & Maintenance

- **Onboarding:** New contributors should read this README and review the main service classes.
- **Extending:** Follow the modular structure and add new features as separate, well-documented classes or modules.
- **Support:** For questions, contact the maintainers or open an issue in the main repository.

---

## 📄 License

This module is part of the CropSense ML API and is subject to the project's main license.
