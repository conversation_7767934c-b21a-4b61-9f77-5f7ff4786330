import os
from datetime import datetime
from dotenv import load_dotenv
from sqlalchemy import Column
from sqlalchemy.sql.sqltypes import Integer, Float, String, Date, DateTime, Boolean, Text, JSON
from sqlalchemy.ext.declarative import declarative_base


Base = declarative_base()

# Load environment variables from .env file
load_dotenv()
DB_SCHEMA = os.getenv("DB_SCHEMA") or "dev"


class Users(Base):
    __tablename__ = 'users'
    __table_args__ = {'schema': DB_SCHEMA}

    user_id = Column(String, primary_key=True, unique=True, nullable=False)
    email = Column(String, unique=True, nullable=False)
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)
    hashed_password = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    is_verified = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f"<User {self.email}>"

    def __str__(self):
        return f"{self.email}"

    def get_full_name(self):
        return f"{self.first_name} {self.last_name}"


class ApiLogs(Base):
    __tablename__ = 'ml_api_requests_logs'
    __table_args__ = {'schema': DB_SCHEMA}

    id = Column(Integer, primary_key=True, unique=True, nullable=False)
    user_id = Column(String, nullable=False)
    ip_address = Column(String, nullable=False)
    path = Column(String, nullable=False)
    request_body = Column(JSON, nullable=True)
    response_body = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f"<ApiRequestLogs {self.id}>"

    def __str__(self):
        return f"{self.id}"
    